# 管理员端API接口文档

## 📋 概述
本文档描述了管理员端前端(admin.js)需要的所有后端API接口。所有时间相关的数据都使用时间戳格式进行传输。

## 🔐 通用请求头
所有接口都需要在请求头中包含用户token：
```
user_token: {JWT_TOKEN}
```

## 📚 接口列表

### 1. 课程管理

#### 1.1 获取课程列表
**接口路径**: `GET /course/list`

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    {
      "id": 1,
      "courseName": "高等数学A",
      "teacher": "张教授",
      "time": 1705276800000,
      "classroom": "A101",
      "capacity": 50,
      "selectedCount": 15
    }
  ]
}
```

#### 1.2 获取单个课程详情
**接口路径**: `GET /course/{courseId}`

**路径参数**:
- `courseId`: 课程ID

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": {
    "id": 1,
    "courseName": "高等数学A",
    "teacher": "张教授",
    "time": 1705276800000,
    "classroom": "A101",
    "capacity": 50,
    "selectedCount": 15
  }
}
```

#### 1.3 添加课程
**接口路径**: `POST /course`

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "courseName": "高等数学A",
  "teacher": "张教授",
  "time": 1705276800000,
  "classroom": "A101",
  "capacity": 50
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "课程添加成功",
  "data": {
    "id": 1
  }
}
```

#### 1.4 编辑课程
**接口路径**: `PUT /course/{courseId}`

**路径参数**:
- `courseId`: 课程ID

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "courseName": "高等数学A",
  "teacher": "张教授",
  "time": 1705276800000,
  "classroom": "A101",
  "capacity": 50
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "课程更新成功",
  "data": null
}
```

#### 1.5 删除课程
**接口路径**: `DELETE /course/{courseId}`

**路径参数**:
- `courseId`: 课程ID

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "课程删除成功",
  "data": null
}
```

#### 1.6 批量删除课程
**接口路径**: `DELETE /course/batch-delete`

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "ids": [1, 2, 3]
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": null
}
```

#### 1.7 导出课程Excel
**接口路径**: `GET /course/export`

**请求头**:
```
user_token: {token}
```

**响应格式**:
- 直接返回Excel文件流
- Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
- Content-Disposition: attachment; filename="课程列表.xlsx"

### 2. 学生管理

#### 2.1 获取学生列表
**接口路径**: `GET /api/admin/students`

**请求参数**:
- `search` (可选): 搜索关键词
- `status` (可选): 学生状态 (all/active/frozen)

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    {
      "studentId": "2021001",
      "name": "张三",
      "status": "active",
      "courseCount": 3
    }
  ]
}
```

#### 2.2 切换学生状态
**接口路径**: `PUT /api/admin/students/{studentId}/status`

**路径参数**:
- `studentId`: 学生ID

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "status": "frozen"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

#### 2.3 重置学生密码
**接口路径**: `PUT /api/admin/students/{studentId}/password`

**路径参数**:
- `studentId`: 学生ID

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "password": "123456"
  }
}
```

### 3. 用户注册

#### 3.1 注册新用户
**接口路径**: `POST /user/register`

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "userId": "2024001",
  "username": "新用户",
  "password": "123456",
  "identity": "STUDENT",
  "email": "<EMAIL>",
  "phoneNumber": "13800000000"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 123
  }
}
```

## 📝 字段说明

### 课程对象字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | Long | 课程ID | `1` |
| `courseName` | String | 课程名称 | `"高等数学A"` |
| `teacher` | String | 授课教师 | `"张教授"` |
| `time` | Long | **上课时间戳** | `1705276800000` |
| `classroom` | String | 教室 | `"A101"` |
| `capacity` | Integer | 课程容量 | `50` |
| `selectedCount` | Integer | 已选人数 | `15` |

### 学生对象字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `studentId` | String | 学号 | `"2021001"` |
| `name` | String | 学生姓名 | `"张三"` |
| `status` | String | 状态 | `"active"` / `"frozen"` |
| `courseCount` | Integer | 已选课程数 | `3` |

### 用户身份枚举
| 值 | 说明 |
|----|------|
| `STUDENT` | 学生 |
| `ADMIN` | 管理员 |

## ⏰ 时间戳说明

所有时间相关的数据都使用Unix时间戳（毫秒）格式：
- 前端发送给后端：时间戳
- 后端返回给前端：时间戳
- 前端显示：使用 `getFormatTime(timestamp)` 函数转换

例如：
- 时间戳: `1705276800000`
- 对应时间: `2024-01-15 08:00:00`
- 前端显示: `2024-01-15 08:00`

## 🚫 错误响应格式

所有接口在出错时返回统一格式：
```json
{
  "code": 400,
  "message": "具体错误信息",
  "data": null
}
```

### 常见错误码
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔄 前端处理逻辑

### 状态码检查
```javascript
if (!response.ok) throw new Error('请求失败');
const data = await response.json();
if (data.code !== 200) throw new Error(data.message || '操作失败');
```

### 时间戳转换
```javascript
// 发送时间戳
time: new Date(timeValue).getTime()

// 显示格式化时间
getFormatTime(timestamp)
```

### 错误处理
```javascript
catch (error) {
    showToast(error.message, 'danger');
}
```
