# 学生端前端API接口文档

## 概述
本文档描述了学生端前端需要的后端API接口。所有时间相关的数据都使用时间戳格式进行传输。

## 接口列表

### 1. 获取教师列表（过滤器用）
**接口路径**: `GET /api/filters/teachers`

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    "张教授",
    "李教授",
    "王教授"
  ]
}
```

### 2. 获取时间段列表（过滤器用）
**接口路径**: `GET /api/filters/times`

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    {
      "timestamp": 1705276800000,
      "description": "周一 08:00"
    },
    {
      "timestamp": 1705284000000,
      "description": "周一 10:00"
    }
  ]
}
```

### 3. 获取课程列表
**接口路径**: `GET /course/list`

**请求参数**:
- `search` (可选): 搜索关键词
- `teacher` (可选): 教师名称
- `time` (可选): 时间戳
- `sort` (可选): 排序方式 (name/capacity)

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    {
      "id": 1,
      "courseName": "高等数学A",
      "teacher": "张教授",
      "time": 1705276800000,
      "classroom": "A101",
      "capacity": 50,
      "selectedCount": 15,
      "selected": false
    }
  ]
}
```

### 4. 选课
**接口路径**: `POST /course-selection/select`

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "courseId": 1
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "选课成功",
  "data": null
}
```

### 5. 退课
**接口路径**: `POST /course-selection/drop`

**请求头**:
```
Content-Type: application/json
user_token: {token}
```

**请求体**:
```json
{
  "courseId": 1
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "退课成功",
  "data": null
}
```

### 6. 获取个人课表
**接口路径**: `GET /course-selection/schedule`

**请求头**:
```
user_token: {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": null,
  "data": [
    {
      "id": 1,
      "courseName": "高等数学A",
      "teacher": "张教授",
      "time": 1705276800000,
      "classroom": "A101"
    }
  ]
}
```

## 时间戳说明

所有时间相关的数据都使用Unix时间戳（毫秒）格式：
- 前端发送给后端：时间戳
- 后端返回给前端：时间戳
- 前端显示：使用 `getFormatTime(timestamp)` 函数转换为可读格式

例如：
- 时间戳: `1705276800000`
- 对应时间: `2024-01-15 08:00:00`
- 前端显示: `2024-01-15 08:00`

## 错误处理

所有接口在出错时返回格式：
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

常见错误码：
- 200: 成功
- 400: 请求参数错误
- 401: 未登录或token无效
- 403: 权限不足
- 500: 服务器内部错误
