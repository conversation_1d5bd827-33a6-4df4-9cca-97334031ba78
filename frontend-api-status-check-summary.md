# 前端API状态码检查修改总结

## 📋 修改概述

基于现在的前后端交互，我为所有的API调用添加了统一的接口响应状态码检查，确保前端能够正确处理后端返回的状态码。

## 🔧 修改的文件

### 1. student.js (学生端)
- ✅ 已在之前的修改中完成状态码检查
- ✅ 所有API调用都检查 `data.code !== 200`
- ✅ 时间戳统一处理

### 2. admin.js (管理员端)
- ✅ 新增统一的状态码检查
- ✅ 优化时间戳处理
- ✅ 更新API接口路径

## 📝 具体修改内容

### 🎯 状态码检查模式

所有API调用现在都遵循以下模式：

```javascript
try {
    const response = await fetch('/api/endpoint', {
        // ... 请求配置
    });

    // 1. 检查HTTP状态码
    if (!response.ok) throw new Error('请求失败');

    // 2. 解析JSON响应
    const data = await response.json();
    
    // 3. 检查业务状态码
    if (data.code !== 200) throw new Error(data.message || '操作失败');

    // 4. 处理成功响应
    // ... 业务逻辑
} catch (error) {
    showToast(error.message, 'danger');
}
```

### 🔄 修改的函数列表

#### admin.js 中修改的函数：

1. **checkAuth()** - 优化用户名显示
2. **loadCourses()** - 添加状态码检查
3. **renderCourses()** - 支持时间戳显示和字段名兼容
4. **loadStudents()** - 添加状态码检查
5. **editCourse()** - 添加状态码检查和时间戳转换
6. **saveCourse()** - 添加状态码检查和时间戳处理
7. **deleteCourse()** - 添加状态码检查
8. **batchDelete()** - 添加状态码检查
9. **exportToExcel()** - 优化文件下载处理
10. **toggleStudentStatus()** - 添加状态码检查
11. **resetPassword()** - 添加状态码检查
12. **registerUser()** - 添加状态码检查和字段优化

### 🕐 时间戳处理优化

#### 1. 新增 getFormatTime() 函数
```javascript
function getFormatTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';
    
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();

    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}
```

#### 2. 时间戳转换逻辑
- **前端 → 后端**: `new Date(timeValue).getTime()` (转为时间戳)
- **后端 → 前端**: `getFormatTime(timestamp)` (转为可读格式)
- **表单填充**: 时间戳转为 `datetime-local` 格式

### 🔗 API接口路径优化

#### 课程相关接口：
- 获取课程列表: `GET /course/list`
- 添加课程: `POST /course`
- 编辑课程: `PUT /course/{id}`
- 删除课程: `DELETE /course/{id}`
- 批量删除: `DELETE /course/batch-delete`
- 导出Excel: `GET /course/export`

#### 用户相关接口：
- 用户注册: `POST /user/register`
- 学生管理: `/api/admin/students/*` (保持不变)

### 📊 字段名兼容性

为了兼容不同的后端返回格式，添加了字段名兼容处理：

```javascript
// 支持 courseName 或 name
courseName: course.courseName || course.name

// 支持 username 或 name
username: user.username || user.name
```

### ⚠️ 错误处理增强

1. **双重检查**: HTTP状态码 + 业务状态码
2. **友好提示**: 优先显示后端返回的错误信息
3. **降级处理**: 提供默认错误信息作为备选

### 🎯 特殊处理

#### 1. 文件下载 (exportToExcel)
```javascript
// 文件下载不需要检查JSON响应码
const blob = await response.blob();
// ... 下载逻辑
```

#### 2. 用户注册 (registerUser)
```javascript
// 添加必需的邮箱和手机号字段
body: JSON.stringify({
    userId,
    username: userName,
    password,
    identity,
    email: `${userId}@example.com`, // 临时邮箱
    phoneNumber: '13800000000' // 临时手机号
})
```

## ✅ 验证要点

1. **状态码检查**: 所有API调用都检查 `data.code !== 200`
2. **错误处理**: 显示具体的错误信息
3. **时间处理**: 前后端交互使用时间戳，前端显示格式化时间
4. **兼容性**: 支持不同的字段名格式
5. **用户体验**: 提供清晰的成功/失败反馈

## 🔄 后续工作

1. 确保后端接口返回统一的响应格式
2. 验证时间戳的时区处理
3. 测试所有API调用的错误场景
4. 确认字段名的一致性
