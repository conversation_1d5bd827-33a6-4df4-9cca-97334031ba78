package com.example.coursesystem.controller;

import com.example.coursesystem.common.errorcode.ControllerErrorCodeConstants;
import com.example.coursesystem.common.exception.ControllerException;
import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.UserPasswordLoginParam;
import com.example.coursesystem.controller.param.UserRegisterParam;
import com.example.coursesystem.controller.result.UserLoginResult;
import com.example.coursesystem.controller.result.UserRegisterResult;
import com.example.coursesystem.service.UserService;
import com.example.coursesystem.service.dto.UserLoginDTO;
import com.example.coursesystem.service.dto.UserRegisterDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;


    /**
     * 用户注册
     * @param userRegisterParam 注册参数
     * @return
     */
    @PostMapping("/register")
    public CommonResult<UserRegisterResult> register(@RequestBody @Validated UserRegisterParam userRegisterParam) {
        if (null == userRegisterParam) {
            throw new ControllerException(ControllerErrorCodeConstants.USER_REGISTER_PARAM_IS_NULL);
        }
        log.info("用户注册:{}", JacksonUtil.writeValueAsString(userRegisterParam));
        // 调用 service
        UserRegisterDTO userRegisterDTO = userService.register(userRegisterParam);
        // 通过 convert 方法进行转换, 随后返回成功结果
        return CommonResult.success(convertToUserRegisterResult(userRegisterDTO));
    }

    @PostMapping("/login/password")
    public CommonResult<UserLoginResult> loginByPassword(@RequestBody UserPasswordLoginParam userPasswordLoginParam) {
        if (null == userPasswordLoginParam) {
            throw new ControllerException(ControllerErrorCodeConstants.USER_PASSWORD_LOGIN_PARAM_IS_NULL);
        }
        log.info("用户密码登录, userPasswordLoginParam:{}", JacksonUtil.writeValueAsString(userPasswordLoginParam));
        UserLoginDTO userLoginDTO = userService.loginByPassword(userPasswordLoginParam);
        return CommonResult.success(convertToUserLoginResult(userLoginDTO));
    }

    /**
     * 将 UserLoginDTO 转换为 UserLoginResult
     * @param userLoginDTO
     * @return
     */
    private UserLoginResult convertToUserLoginResult(UserLoginDTO userLoginDTO) {
        if(null == userLoginDTO){
            throw new ControllerException(ControllerErrorCodeConstants.USER_LOGIN_ERROR);
        }
        UserLoginResult userLoginResult = new UserLoginResult();
        BeanUtils.copyProperties(userLoginDTO, userLoginResult);
        return userLoginResult;
    }

    /**
     * 将 UserRegisterDTO 转换为 UserRegisterResult
     * @param userRegisterDTO
     * @return
     */
    private UserRegisterResult convertToUserRegisterResult(UserRegisterDTO userRegisterDTO) {
        if(null == userRegisterDTO){
            throw new ControllerException(ControllerErrorCodeConstants.USER_REGISTER_ERROR);
        }
        UserRegisterResult userRegisterResult = new UserRegisterResult();
        userRegisterResult.setUserId(userRegisterDTO.getUserId());
        return userRegisterResult;
    }
}
