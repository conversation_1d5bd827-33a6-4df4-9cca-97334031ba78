package com.example.coursesystem.controller;

import com.example.coursesystem.common.errorcode.ControllerErrorCodeConstants;
import com.example.coursesystem.common.exception.ControllerException;
import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.CourseAddParam;
import com.example.coursesystem.controller.param.CourseListParam;
import com.example.coursesystem.controller.param.CourseUpdateParam;
import com.example.coursesystem.controller.result.CourseFilterResult;
import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.service.CourseService;
import com.example.coursesystem.service.dto.CourseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    @PostMapping("/add")
    public CommonResult<Long> addCourse(@RequestBody @Validated CourseAddParam param) {
        if (param == null) {
            throw new ControllerException(ControllerErrorCodeConstants.ADD_COURSE_PARAM_IS_NULL);
        }
        return CommonResult.success(courseService.addCourse(param));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateCourse(@RequestBody @Validated CourseUpdateParam param) {
        if (param == null) {
            throw new ControllerException(ControllerErrorCodeConstants.UPDATE_COURSE_PARAM_IS_NULL);
        }
        return CommonResult.success(courseService.updateCourse(param));
    }

    @PostMapping("/delete/{id}")
    public CommonResult<Boolean> deleteCourse(@PathVariable Long id) {
        courseService.deleteCourse(id);
        return CommonResult.success(Boolean.TRUE);
    }

    @DeleteMapping("/batch")
    public CommonResult<Boolean> batchDeleteCourses(@RequestBody List<Long> ids) {
        courseService.batchDeleteCourses(ids);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/list")
    public CommonResult<List<CourseResult>> listCourses(CourseListParam requestParam) {
        if (requestParam == null) {
            throw new ControllerException(ControllerErrorCodeConstants.LIST_COURSE_PARAM_IS_NULL);
        }
        log.info("获取课程列表, requestParam: {}", JacksonUtil.writeValueAsString(requestParam));
        List<CourseResult> courseResults = covertToCourseListResult(courseService.listCourses(requestParam));
        log.info("获取课程列表成功: {}", JacksonUtil.writeValueAsString(courseResults));
        return CommonResult.success(courseResults);
    }

    @GetMapping("/get/{id}")
    public CommonResult<CourseResult> getSingleCourse(@PathVariable Long id) {
        CourseResult courseResult = new CourseResult();
        BeanUtils.copyProperties(courseService.getCourse(id), courseResult);
        return CommonResult.success(courseResult);
    }

    private List<CourseResult> covertToCourseListResult(List<CourseDTO> courseDTOS) {
        return courseDTOS.stream().map(courseDTO -> {
            CourseResult result = new CourseResult();
            BeanUtils.copyProperties(courseDTO, result);
            result.setTime(courseDTO.getTime().getTime());
            return result;
        }).collect(Collectors.toList());
    }

    @GetMapping("/filter/teacher")
    public CommonResult<CourseFilterResult<String>> getTeacherList() {
        return CommonResult.success(new CourseFilterResult<>(courseService.getTeacherList().getFilterData()));
    }

    @GetMapping("/filter/time")
    public CommonResult<CourseFilterResult<Long>> getTimeList() {
        return CommonResult.success(new CourseFilterResult<>(courseService.getTimeList().getFilterData()));
    }
} 