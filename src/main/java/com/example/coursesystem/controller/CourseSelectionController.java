package com.example.coursesystem.controller;

import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.service.CourseSelectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/course-selection")
public class CourseSelectionController {

    @Autowired
    private CourseSelectionService courseSelectionService;

    @RequestMapping("/select/{courseId}")
    public CommonResult<Boolean> selectCourse(@PathVariable Long courseId) {
        courseSelectionService.selectCourse(courseId);
        return CommonResult.success(Boolean.TRUE);
    }

    @RequestMapping("/delete/{courseId}")
    public CommonResult<Boolean> dropCourse(@PathVariable Long courseId) {
        courseSelectionService.dropCourse(courseId);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/my-courses")
    public CommonResult<List<CourseResult>> getMyCourses() {
        return CommonResult.success(courseSelectionService.getMyCourses());
    }
} 