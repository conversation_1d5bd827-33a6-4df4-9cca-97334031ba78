package com.example.coursesystem.controller;

import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.controller.result.CourseScheduleResult;
import com.example.coursesystem.service.CourseSelectionService;
import com.example.coursesystem.service.dto.CourseScheduleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/course-selection")
public class CourseSelectionController {

    @Autowired
    private CourseSelectionService courseSelectionService;

    @RequestMapping("/select/{courseId}")
    public CommonResult<Boolean> selectCourse(@PathVariable Long courseId) {
        courseSelectionService.selectCourse(courseId);
        return CommonResult.success(Boolean.TRUE);
    }

    @RequestMapping("/delete/{courseId}")
    public CommonResult<Boolean> dropCourse(@PathVariable Long courseId) {
        courseSelectionService.dropCourse(courseId);
        return CommonResult.success(Boolean.TRUE);
    }

    @GetMapping("/schedule")
    public CommonResult<List<CourseScheduleResult>> getSchedule() {
        return CommonResult.success(convertToCourseScheduleResult(courseSelectionService.getSchedule()));
    }

    private List<CourseScheduleResult> convertToCourseScheduleResult(List<CourseScheduleDTO> courseScheduleDTOS) {
        return courseScheduleDTOS.stream()
                .map(courseScheduleDTO -> {
                    CourseScheduleResult result = new CourseScheduleResult();
                    BeanUtils.copyProperties(courseScheduleDTO, result);
                    return result;
                })
                .toList();
    }
}