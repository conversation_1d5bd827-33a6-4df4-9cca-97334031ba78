package com.example.coursesystem.controller;

import com.example.coursesystem.common.errorcode.ControllerErrorCodeConstants;
import com.example.coursesystem.common.exception.ControllerException;
import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.StudentListParam;
import com.example.coursesystem.controller.result.StudentResult;
import com.example.coursesystem.service.StudentService;
import com.example.coursesystem.service.dto.StudentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class StudentController {

    @Autowired
    StudentService studentService;

    @RequestMapping("/admin/student")
    public CommonResult<List<StudentResult>> getStudentList(StudentListParam param) {
        if (param == null) {
            throw new ControllerException(ControllerErrorCodeConstants.STUDENT_LIST_PARAM_IS_NULL);
        }
        log.info("获取学生列表, param: {}", JacksonUtil.writeValueAsString(param));

        List<StudentResult> result = convertToStudentResult(studentService.listStudents(param));
        return CommonResult.success(result);
    }

    private List<StudentResult> convertToStudentResult(List<StudentDTO> studentDTOS) {
        return studentDTOS.stream()
                .map(studentDTO -> {
                    StudentResult result = new StudentResult();
                    BeanUtils.copyProperties(studentDTO, result);
                    return result;
                })
                .toList();
    }
}
