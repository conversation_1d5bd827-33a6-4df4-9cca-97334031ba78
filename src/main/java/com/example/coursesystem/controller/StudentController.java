package com.example.coursesystem.controller;

import com.example.coursesystem.common.errorcode.ControllerErrorCodeConstants;
import com.example.coursesystem.common.exception.ControllerException;
import com.example.coursesystem.common.pojo.CommonResult;
import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.StudentListParam;
import com.example.coursesystem.controller.result.StudentResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class StudentController {

    @RequestMapping("/admin/student")
    public CommonResult<List<StudentResult>> getStudentList(StudentListParam param) {
        if (param == null) {
            throw new ControllerException(ControllerErrorCodeConstants.STUDENT_LIST_PARAM_IS_NULL);
        }
        log.info("获取学生列表, param: {}", JacksonUtil.writeValueAsString(param));


        return CommonResult.success();
    }
}
