package com.example.coursesystem.controller.param;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

@Data
public class CourseUpdateParam {
    @NotNull(message = "课程ID不能为空")
    private Long id;

    @NotBlank(message = "课程名称不能为空")
    private String name;

    @NotBlank(message = "教师名称不能为空")
    private String teacher;

    @NotBlank(message = "上课时间不能为空")
    private String time;

    @NotBlank(message = "教室不能为空")
    private String classroom;

    @NotNull(message = "容量不能为空")
    @Positive(message = "容量必须大于0")
    private Integer capacity;
} 