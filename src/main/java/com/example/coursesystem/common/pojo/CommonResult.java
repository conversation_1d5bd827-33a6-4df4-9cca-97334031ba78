package com.example.coursesystem.common.pojo;


import com.example.coursesystem.common.errorcode.ErrorCode;
import com.example.coursesystem.common.errorcode.GlobalErrorCodeConstants;
import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serializable;

@Data
public class CommonResult<T> implements Serializable {
    Integer code;
    String message;
    T data;


    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.data = data;
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        return result;
    }

    public static <T> CommonResult<T> error(String message) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.FAILURE.getCode();
        result.message = message;
        return result;
    }

    public static <T> CommonResult<T> error(Integer code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code),
                "错误码异常");
        return error(message);
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getMessage());
    }

}
