package com.example.coursesystem.common.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class LoginInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private LoginInterceptor loginInterceptor;

    private static final String[] EXCLUDE_PATH = {
            "/favicon.ico",
            "/css/**",
            "/js/**",
            "/pic/**",
            "/**/*.jpg",
            "/**/*.png",
            "/",
            "/**/*.html",
            "/user/login/**",
            "/user/register",
    };


    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(EXCLUDE_PATH);
    }
}
