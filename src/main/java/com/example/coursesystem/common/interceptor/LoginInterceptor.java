package com.example.coursesystem.common.interceptor;

import com.example.coursesystem.common.constant.UserLoginConstants;
import com.example.coursesystem.common.util.JWTUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;

@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler){
        String token = request.getHeader(UserLoginConstants.USER_TOKEN_HEADER);
        log.info("获取token: {}, URL: {}", token, request.getRequestURL());
        Map<String, Object> map = JWTUtil.parseJWT(token);
        if (map == null || map.get(UserLoginConstants.USER_TOKEN_MAP_ID) == null) {
            log.error("JWT 解析失败");
            response.setStatus(401);
            return false;
        }

        return true;
    }
}
