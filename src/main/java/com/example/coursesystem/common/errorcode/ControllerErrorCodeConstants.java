package com.example.coursesystem.common.errorcode;

public interface ControllerErrorCodeConstants {
    // 用户模块
    ErrorCode USER_REGISTER_PARAM_IS_NULL = new ErrorCode(101, "用户注册参数为空");
    ErrorCode USER_REGISTER_ERROR = new ErrorCode(102, "用户注册失败");
    ErrorCode USER_LOGIN_ERROR = new ErrorCode(104, "用户登录失败");
    ErrorCode USER_PASSWORD_LOGIN_PARAM_IS_NULL = new ErrorCode(105, "用户密码登录参数为空");
    // 课程模块
    ErrorCode ADD_COURSE_PARAM_IS_NULL = new ErrorCode(201, "添加课程参数为空");
    ErrorCode UPDATE_COURSE_PARAM_IS_NULL = new ErrorCode(202, "更新课程参数为空");
    ErrorCode LIST_COURSE_PARAM_IS_NULL = new ErrorCode(203, "获取课程列表参数为空");
    ErrorCode EXPORT_COURSE_ERROR = new ErrorCode(204, "导出课程失败");
}
