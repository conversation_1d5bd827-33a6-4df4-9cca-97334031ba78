package com.example.coursesystem.common.errorcode;

public interface ServiceErrorCodeConstants {
    // 用户模块
    ErrorCode USER_REGISTER_PARAM_IS_NULL = new ErrorCode(101, "用户注册参数为空");
    ErrorCode EMAIL_FORMAT_ERROR = new ErrorCode(102, "邮箱格式错误");
    ErrorCode PHONE_NUMBER_FORMAT_ERROR = new ErrorCode(103, "手机号格式错误");
    ErrorCode USER_IDENTITY_ERROR = new ErrorCode(104, "用户身份错误");
    ErrorCode PASSWORD_EMPTY_ERROR = new ErrorCode(105, "用户密码为空");
    ErrorCode PASSWORD_FORMAT_ERROR = new ErrorCode(106, "用户密码格式错误");
    ErrorCode EMAIL_EXIST_ERROR = new ErrorCode(107, "用户邮箱已存在");
    ErrorCode PHONE_NUMBER_EXIST_ERROR = new ErrorCode(108, "用户手机号已存在");
    ErrorCode PASSWORD_ERROR = new ErrorCode(112, "用户密码错误");
    ErrorCode LOGIN_PARAM_IS_NULL = new ErrorCode(113, "用户登录参数为空");
    ErrorCode PHONE_NUMBER_EMPTY_ERROR = new ErrorCode(114, "用户手机号为空");
    ErrorCode USER_NOT_EXIST = new ErrorCode(115, "用户不存在");
    ErrorCode USER_NOT_LOGIN = new ErrorCode(116, "用户未登录");

    // 课程模块
    ErrorCode UPDATE_COURSE_ERROR = new ErrorCode(201, "更新课程失败");
    ErrorCode COURSE_NOT_FOUND = new ErrorCode(202, "课程不存在");
    ErrorCode DELETE_COURSE_ERROR = new ErrorCode(203, "删除课程失败");

    // 选课模块
    ErrorCode COURSE_SELECTION_EXISTS = new ErrorCode(301, "课程已选");
    ErrorCode COURSE_CAPACITY_FULL = new ErrorCode(302, "课程容量已满");
}
