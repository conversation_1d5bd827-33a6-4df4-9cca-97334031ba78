package com.example.coursesystem.common.util;

import cn.hutool.core.lang.RegexPool;

public class RegexUtil {
    public static boolean isPhoneNumber(String phoneNumber) {
        String regex = RegexPool.MOBILE;
        return phoneNumber.matches(regex);
    }

    public static boolean isEmail(String email) {
        String regex = RegexPool.EMAIL;
        return email.matches(regex);
    }

    public static boolean isPassword(String password) {
        // 密码由数字、字母、特殊字符组成，长度在 6-16 位之间
        String regex = "^[a-zA-Z0-9~!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?]{6,16}$";
        return password.matches(regex);
    }
}
