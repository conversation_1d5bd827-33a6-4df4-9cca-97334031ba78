package com.example.coursesystem.common.util;


import com.example.coursesystem.common.config.JWTProperties;
import com.example.coursesystem.common.constant.UserLoginConstants;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class JWTUtil {
    @Value("${jwt.secret-key}")
    private static String SECRET_KEY;

    @Value("${jwt.expire-time}")
    private static Long EXPIRE_TIME;

    private static Key JWT_KEY;

    @Autowired
    public JWTUtil(JWTProperties jwtProperties) {
        SECRET_KEY = Objects.requireNonNull(jwtProperties.getSecretKey());
        EXPIRE_TIME = Objects.requireNonNull(jwtProperties.getExpireTime());
        JWT_KEY = Keys.hmacShaKeyFor(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
    }


    public static String createJWT(Map<String, Object> map) {
        return Jwts.builder()
                .setClaims(map)
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRE_TIME))
                .signWith(JWT_KEY)
                .compact();
    }

    public static Map<String, Object> parseJWT(String jwt) {
        if (!StringUtils.hasText(jwt)) {
            return null;
        }

        Map<String, Object> ret = null;
        try {
            ret = Jwts.parserBuilder()
                    .setSigningKey(JWT_KEY)
                    .build()
                    .parseClaimsJws(jwt)
                    .getBody();
        }catch (ExpiredJwtException e){
            log.info("JWT 已过期, token:{}", jwt);
        }catch (Exception e){
            log.error("JWT 解析失败", e);
        }

        return ret;
    }

    public static Long getUserId(String jwt){
        if (!StringUtils.hasText(jwt)) {
            return null;
        }
        return ((Integer) (Objects.requireNonNull(parseJWT(jwt)).get(UserLoginConstants.USER_TOKEN_MAP_ID))).longValue();
    }

}
