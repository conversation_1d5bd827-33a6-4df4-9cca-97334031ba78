package com.example.coursesystem.common.util;

import com.example.coursesystem.service.dto.CourseDTO;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.Date;
import java.util.List;

public class ExcelGenerateUtil {
    public static XSSFWorkbook generateCourseExcel(List<CourseDTO> courses) {
        // 创建工作簿和工作表
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("课程列表");

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"课程ID", "课程名称", "教师", "开课时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据行
        int rowNum = 1;
        for (CourseDTO course : courses) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(course.getId());
            row.createCell(1).setCellValue(course.getCourseName());
            row.createCell(2).setCellValue(course.getTeacher());
            row.createCell(3).setCellValue(course.getTime().getTime());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        return workbook;
    }
}
