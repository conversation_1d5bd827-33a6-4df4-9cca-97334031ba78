package com.example.coursesystem.common.util;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
public class SecurityUtil {

    private static final String SECRET_KEY = "a/Ov9Fc[/2POkL/2";
    private static final AES aes = SecureUtil.aes(SECRET_KEY.getBytes());

    /**
     * 对密码进行加密
     * @param inputPassword 原始密码
     * @return 加密并且和盐拼接后的密码
     */
    public static String encryptPassword(String inputPassword) {
        // 生成随机盐
        String randomSalt = UUID.randomUUID().toString().replace("-", "");

        // 随后将盐和密码进行拼接, 再使用 MD5 算法进行加密
        String encryptedPassword = DigestUtils.md5DigestAsHex((inputPassword + randomSalt).getBytes(StandardCharsets.UTF_8));
        // 将盐和加密后的密码进行拼接, 作为最终的加密结果返回
        return encryptedPassword + randomSalt;
    }


    /**
     * 对密码进行校验
     * @param inputPassword 原始密码
     * @param encryptedPassword 数据库中加密后的密码
     * @return 校验结果
     */
    public static boolean checkPassword(String inputPassword, String encryptedPassword) {
        // 从加密后的密码中分离出盐和加密后的密码
        String salt = encryptedPassword.substring(32);

        // 对原始密码进行加密
        String newEncryptedPassword = DigestUtils.md5DigestAsHex((inputPassword + salt).getBytes(StandardCharsets.UTF_8));

        // 比较新加密的密码和原始密码是否相等
        return encryptedPassword.equals(newEncryptedPassword + salt);
    }


    /**
     * 对手机号进行加密
     * @param phoneNumber 手机号
     * @return 加密后的手机号
     */
    public static String getEncryptedPhoneNumber(String phoneNumber) {
        return aes.encryptHex(phoneNumber);
    }

    /**
     * 对手机号进行解密
     * @param encryptedPhoneNumber 加密后的手机号
     * @return 解密后的手机号
     */
    public static String getDecryptedPhoneNumber(String encryptedPhoneNumber) {
        return aes.decryptStr(encryptedPhoneNumber);
    }
}
