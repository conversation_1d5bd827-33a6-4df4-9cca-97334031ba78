package com.example.coursesystem.common.util;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.json.JsonParseException;

import java.util.List;
import java.util.concurrent.Callable;

public class JacksonUtil {
    // 单例 ObjectMapper
    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
    }

    private JacksonUtil() {
    }

    // 序列化
    public static String writeValueAsString(Object obj) {
        return tryParse(() -> objectMapper.writeValueAsString(obj));
    }

    // 普通对象反序列化
    public static <T> T readValue(String json, Class<T> clazz) {
        return tryParse(() -> objectMapper.readValue(json, clazz));
    }

    // 列表反序列化
    public static <T> T readValue(String json, Class<?>... elementClasses) {
        return tryParse(() -> objectMapper.readValue(json,
                objectMapper.getTypeFactory().constructParametricType(List.class, elementClasses)));
    }

    // 封装方法，集中处理异常
    private static <T> T tryParse(Callable<T> parser) {
        try {
            return parser.call();
        } catch (Exception e) {
            if (JsonParseException.class.isAssignableFrom(e.getClass())) {
                throw new JsonParseException(e);
            }
            throw new IllegalArgumentException(e);
        }
    }
}
