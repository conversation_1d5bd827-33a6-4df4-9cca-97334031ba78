package com.example.coursesystem.dao.dataobject;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class CourseSelectionDO extends BaseDO{
    private Long userId;
    private Long courseId;
    private String courseName;
    private String teacher;
    private Date time;
    private String classroom;
}