package com.example.coursesystem.dao.dataobject;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.PriorityQueue;

@Data
@EqualsAndHashCode(callSuper = true)
public class CourseDO extends BaseDO {
    private String courseName;
    private String teacher;
    private Date time;
    private String classroom;
    private Integer capacity;
    private Integer selectedCount;
}