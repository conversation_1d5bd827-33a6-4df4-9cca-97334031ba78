package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.dao.dataobject.UserDO;
import jakarta.validation.constraints.NotBlank;
import org.apache.ibatis.annotations.*;

@Mapper
public interface UserMapper {

    @Select("select count(*) from user where phone_number = #{phoneNumber}")
    Integer countByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    @Select("select count(*) from user where email = #{email}")
    Integer countByMail(@Param("email")String email);

    @Insert("insert into user(user_id,username, password, phone_number, email, identity) values(#{userId}, #{username}, #{password}, #{phoneNumber}, #{email}, #{identity})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    void insertUser(UserDO userDO);


    @Select("select * from user where id = #{id}")
    UserDO selectById(Long id);

    @Select("select * from user where phone_number = #{phoneNumber} or email = #{email} or user_id = #{userId} or username = #{username}")
    UserDO selectByIdentifier(@NotBlank(message = "标识不能为空") String identifier);
}
