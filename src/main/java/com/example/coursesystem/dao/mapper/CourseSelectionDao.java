package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.dao.dataobject.CourseSelectionDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CourseSelectionDao {
    
    @Insert("INSERT INTO course_selection (user_id, course_id, create_time, update_time) " +
            "VALUES (#{userId}, #{courseId}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CourseSelectionDO courseSelection);

    @Select("SELECT * FROM course_selection WHERE user_id = #{userId}")
    List<CourseSelectionDO> findByUserId(Long userId);

    @Select("SELECT * FROM course_selection WHERE course_id = #{courseId}")
    List<CourseSelectionDO> findByCourseId(Long courseId);

    @Delete("DELETE FROM course_selection WHERE user_id = #{userId} AND course_id = #{courseId}")
    int delete(@Param("userId") Long userId, @Param("courseId") Long courseId);

    @Select("SELECT COUNT(*) FROM course_selection WHERE user_id = #{userId}")
    int countByUserId(Long userId);
} 