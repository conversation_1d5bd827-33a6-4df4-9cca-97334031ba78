package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.dao.dataobject.CourseSelectionDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CourseSelectionMapper {

    @Insert("insert into course_user (user_id, course_id, course_name, teacher, time, classroom) " +
            "values(#{userId}, #{courseId}, #{courseName}, #{teacher}, #{time}, #{classroom})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CourseSelectionDO courseSelection);

    @Select("SELECT * FROM course_user WHERE user_id = #{userId}")
    List<CourseSelectionDO> findByUserId(Long userId);

    @Select("SELECT * FROM course_user WHERE course_id = #{courseId}")
    List<CourseSelectionDO> findByCourseId(Long courseId);

    @Delete("DELETE FROM course_user WHERE user_id = #{userId} AND course_id = #{courseId}")
    int delete(@Param("userId") Long userId, @Param("courseId") Long courseId);

    @Select("SELECT COUNT(*) FROM course_user WHERE user_id = #{userId}")
    int countByUserId(Long userId);

    @Select("select count(*) from course_user where course_id = #{courseId}")
    Integer countByCourseId(Long courseId);

    @Select("select count(*) from course_user where user_id = #{userId} and course_id = #{courseId}")
    int countByUserIdAndCourseId(@Param("userId") Long userId, @Param("courseId") Long courseId);

    @Update("update course_user set course_name = #{courseName}, teacher = #{teacher}, time = #{time}, classroom = #{classroom} where course_id = #{courseId}")
    int updateByCourseId(CourseSelectionDO courseSelectionDO);


    @Delete("delete from course_user where course_id = #{id}")
    int deleteByCourseId(Long id);
}