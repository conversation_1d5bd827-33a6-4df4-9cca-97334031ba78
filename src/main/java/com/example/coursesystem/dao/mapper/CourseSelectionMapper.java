package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.dao.dataobject.CourseSelectionDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CourseSelectionMapper {
    
    @Insert("INSERT INTO course_user (user_id, course_id, create_time, update_time) " +
            "VALUES (#{userId}, #{courseId}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CourseSelectionDO courseSelection);

    @Select("SELECT * FROM course_user WHERE user_id = #{userId}")
    List<CourseSelectionDO> findByUserId(Long userId);

    @Select("SELECT * FROM course_user WHERE course_id = #{courseId}")
    List<CourseSelectionDO> findByCourseId(Long courseId);

    @Delete("DELETE FROM course_user WHERE user_id = #{userId} AND course_id = #{courseId}")
    int delete(@Param("userId") Long userId, @Param("courseId") Long courseId);

    @Select("SELECT COUNT(*) FROM course_user WHERE user_id = #{userId}")
    int countByUserId(Long userId);

    @Select("select count(*) from course_user where course_id = #{courseId}")
    Integer countByCourseId(Long courseId);
}