package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.controller.param.CourseListParam;
import com.example.coursesystem.dao.dataobject.CourseDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CourseMapper {
    
    @Insert("INSERT INTO course (name, teacher, time, classroom, capacity, selected_count, create_time, update_time) " +
            "VALUES (#{name}, #{teacher}, #{time}, #{classroom}, #{capacity}, 0, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CourseDO course);

    List<CourseDO> findAll(CourseListParam param);

    @Select("SELECT * FROM course WHERE id = #{id}")
    CourseDO findById(Long id);

    @Update("UPDATE course SET name = #{name}, teacher = #{teacher}, time = #{time}, " +
            "classroom = #{classroom}, capacity = #{capacity}, update_time = NOW() " +
            "WHERE id = #{id}")
    int update(CourseDO course);

    @Delete("DELETE FROM course WHERE id = #{id}")
    int deleteById(Long id);

    @Update("UPDATE course SET selected_count = selected_count + 1 WHERE id = #{id}")
    int incrementSelectedCount(Long id);

    @Update("UPDATE course SET selected_count = selected_count - 1 WHERE id = #{id}")
    int decrementSelectedCount(Long id);
} 