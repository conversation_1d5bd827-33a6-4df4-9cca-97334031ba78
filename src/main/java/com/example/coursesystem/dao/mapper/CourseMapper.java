package com.example.coursesystem.dao.mapper;

import com.example.coursesystem.controller.param.CourseListParam;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.service.query.CourseListQuery;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface CourseMapper {

    @Insert("insert into course(course_name, teacher, time, classroom, capacity) values(#{courseName}, #{teacher}, #{time}, #{classroom}, #{capacity})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CourseDO course);

    List<CourseDO> findAll(CourseListQuery courseListQuery);

    @Select("SELECT * FROM course WHERE id = #{id}")
    CourseDO findById(Long id);

    @Update("update course set course_name = #{courseName}, teacher = #{teacher}, time = #{time}, classroom = #{classroom}, capacity = #{capacity} where id = #{id}")
    int update(CourseDO course);

    @Delete("DELETE FROM course WHERE id = #{id}")
    int deleteById(Long id);

    @Update("UPDATE course SET selected_count = selected_count + 1 WHERE id = #{id}")
    int incrementSelectedCount(Long id);

    @Update("UPDATE course SET selected_count = selected_count - 1 WHERE id = #{id}")
    int decrementSelectedCount(Long id);

    @Select("select distinct time from course")
    List<Date> findCourseTimeList();

    @Select("select distinct teacher from course")
    List<String> findCourseTeacherList();
}