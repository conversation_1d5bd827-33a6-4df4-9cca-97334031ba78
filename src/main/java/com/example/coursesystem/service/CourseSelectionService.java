package com.example.coursesystem.service;

import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.service.dto.CourseScheduleDTO;

import java.util.List;

public interface CourseSelectionService {
    void selectCourse(Long courseId);
    void dropCourse(Long courseId);
    List<CourseScheduleDTO> getSchedule();
    Integer getCourseSelectCount(Long courseId);
    Boolean getCurrentUserSelectCourse(Long courseId);
} 