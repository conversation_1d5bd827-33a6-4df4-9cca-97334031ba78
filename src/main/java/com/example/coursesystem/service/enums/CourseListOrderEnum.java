package com.example.coursesystem.service.enums;

import lombok.Getter;

@Getter
public enum CourseListOrderEnum {
    COURSE_NAME("课程名"),
    REMAIN_CAPACITY("剩余名额");

    private final String orderRule;

    CourseListOrderEnum(String orderRule) {
        this.orderRule = orderRule;
    }

    public static CourseListOrderEnum getOrderRule(String name) {
        for (CourseListOrderEnum c : CourseListOrderEnum.values()) {
            if (c.name().equalsIgnoreCase(name)) {
                return c;
            }
        }
        return null;
    }
}
