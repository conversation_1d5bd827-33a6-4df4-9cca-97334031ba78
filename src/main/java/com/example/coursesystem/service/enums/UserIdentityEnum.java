package com.example.coursesystem.service.enums;

import lombok.Getter;

@Getter
public enum UserIdentityEnum {
    ADMIN("管理员"),
    STUDENT("学生");

    private final String identity;

    UserIdentityEnum(String identity) {
        this.identity = identity;
    }

    public static UserIdentityEnum getIdentity(String name) {
        for (UserIdentityEnum u : UserIdentityEnum.values()) {
            if (u.name().equalsIgnoreCase(name)) {
                return u;
            }
        }
        return null;
    }

}
