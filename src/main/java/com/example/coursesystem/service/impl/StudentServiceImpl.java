package com.example.coursesystem.service.impl;

import com.example.coursesystem.controller.param.StudentListParam;
import com.example.coursesystem.dao.dataobject.UserDO;
import com.example.coursesystem.dao.mapper.UserMapper;
import com.example.coursesystem.service.StudentService;
import com.example.coursesystem.service.dto.StudentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class StudentServiceImpl implements StudentService {


    @Autowired
    UserMapper userMapper;

    @Override
    public List<StudentDTO> listStudents(StudentListParam param) {
        List<UserDO> userDOList = userMapper.listStudents(param);
        return userDOList.stream()
                .map(userDO -> {
                    StudentDTO studentDTO = new StudentDTO();
                    BeanUtils.copyProperties(userDO, studentDTO);
                    studentDTO.setStudentId(userDO.getUserId());
                    studentDTO.setName(userDO.getUsername());
                    return studentDTO;
                })
                .collect(Collectors.toList());
    }
}
