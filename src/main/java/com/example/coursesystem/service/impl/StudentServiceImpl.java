package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.StudentListParam;
import com.example.coursesystem.dao.dataobject.UserDO;
import com.example.coursesystem.dao.mapper.UserMapper;
import com.example.coursesystem.service.CourseSelectionService;
import com.example.coursesystem.service.StudentService;
import com.example.coursesystem.service.dto.StudentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StudentServiceImpl implements StudentService {


    @Autowired
    UserMapper userMapper;

    @Autowired
    CourseSelectionService courseSelectionService;


    @Override
    public List<StudentDTO> listStudents(StudentListParam param) {
        List<UserDO> userDOList = userMapper.listStudents(param);
        log.info("获取学生列表成功: {}", JacksonUtil.writeValueAsString(userDOList));
        return userDOList.stream()
                .map(userDO -> {
                    StudentDTO studentDTO = new StudentDTO();
                    studentDTO.setStatus(userDO.getStatus());
                    studentDTO.setStudentId(userDO.getUserId());
                    studentDTO.setName(userDO.getUsername());
                    try {
                        studentDTO.setCourseCount(courseSelectionService.getCourseSelectCount(userDO.getId()));
                    }catch (Exception e){
                        log.error("获取学生[{}]的选课数量失败: {}", userDO.getUserId(), e.getMessage());
                        studentDTO.setCourseCount(-1);
                    }
                    return studentDTO;
                })
                .collect(Collectors.toList());
    }
}
