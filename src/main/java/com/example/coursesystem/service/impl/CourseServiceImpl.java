package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.errorcode.ServiceErrorCodeConstants;
import com.example.coursesystem.common.exception.ServiceException;
import com.example.coursesystem.common.util.JacksonUtil;
import com.example.coursesystem.controller.param.CourseAddParam;
import com.example.coursesystem.controller.param.CourseListParam;
import com.example.coursesystem.controller.param.CourseUpdateParam;
import com.example.coursesystem.dao.mapper.CourseMapper;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.service.CourseSelectionService;
import com.example.coursesystem.service.CourseService;
import com.example.coursesystem.service.dto.CourseDTO;
import com.example.coursesystem.service.dto.CourseFilterDTO;
import com.example.coursesystem.service.enums.CourseListOrderEnum;
import com.example.coursesystem.service.query.CourseListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CourseServiceImpl implements CourseService {

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CourseSelectionService courseSelectionService;

    @Override
    @Transactional
    public Long addCourse(CourseAddParam param) {
        CourseDO course = new CourseDO();
        BeanUtils.copyProperties(param, course);
        courseMapper.insert(course);
        return course.getId();
    }

    @Override
    @Transactional
    public Boolean updateCourse(CourseUpdateParam param) {
        CourseDO course = courseMapper.findById(param.getId());
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        BeanUtils.copyProperties(param, course);
        if (courseMapper.update(course) == 0) {
            throw new ServiceException(ServiceErrorCodeConstants.UPDATE_COURSE_ERROR);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean deleteCourse(Long id) {
        CourseDO course = courseMapper.findById(id);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        if (courseMapper.deleteById(id) == 0) {
            throw new ServiceException(ServiceErrorCodeConstants.DELETE_COURSE_ERROR);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean batchDeleteCourses(List<Long> ids) {
        ids.forEach(this::deleteCourse);
        return true;
    }

    @Override
    public List<CourseDTO> listCourses(CourseListParam param) {
        checkCourseListParam(param);
        CourseListQuery courseListQuery = convertToCourseListQuery(param);

        log.info("查询课程数据库, courseListQuery: {}", JacksonUtil.writeValueAsString(courseListQuery));

        List<CourseDO> courseDOList = courseMapper.findAll(courseListQuery);
        log.info("查询课程数据库成功: {}", JacksonUtil.writeValueAsString(courseDOList));

        return courseDOList.stream()
                .map(course -> {
                    CourseDTO result = new CourseDTO();
                    BeanUtils.copyProperties(course, result);
                    try {
                        result.setSelectedCount(courseSelectionService.getCourseSelectCount(course.getId()));
                        return result;
                    } catch (Exception e) {
                        log.error("查询课程[{}]的选课人数失败: {}", course.getId(), e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .sorted((c1, c2) -> {
                    if (param.getSort() == null) {
                        return 0;
                    }
                    if (param.getSort().equalsIgnoreCase(CourseListOrderEnum.COURSE_NAME.name())) {
                        return c1.getCourseName().compareTo(c2.getCourseName());
                    }
                    if (param.getSort().equalsIgnoreCase(CourseListOrderEnum.REMAIN_CAPACITY.name())) {
                        return (c1.getCapacity() - c1.getSelectedCount()) - (c2.getCapacity() - c2.getSelectedCount());
                    }
                    return 0;
                })
                .collect(Collectors.toList());
    }

    private CourseListQuery convertToCourseListQuery(CourseListParam param) {
        CourseListQuery courseListQuery = new CourseListQuery();
        BeanUtils.copyProperties(param, courseListQuery);
        if(param.getTime() != null) courseListQuery.setTime(new Date(param.getTime()));
        return courseListQuery;
    }

    private void checkCourseListParam(CourseListParam param) {
        if(null != param.getSort()){
            if(null == CourseListOrderEnum.getOrderRule(param.getSort())){
                throw new ServiceException(ServiceErrorCodeConstants.COURSE_LIST_ORDER_UNSUPPORTED_ERROR);
            }
        }
    }

    @Override
    public CourseDTO getCourse(Long id) {
        CourseDO course = courseMapper.findById(id);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        CourseDTO courseDTO = new CourseDTO();
        BeanUtils.copyProperties(course, courseDTO);
        return courseDTO;
    }

    @Override
    public CourseFilterDTO<String> getTeacherList() {
        List<String> teacherList = courseMapper.findCourseTeacherList();
        log.info("获取教师列表成功: {}", JacksonUtil.writeValueAsString(teacherList));
        return new CourseFilterDTO<>(teacherList);
    }

    @Override
    public CourseFilterDTO<Long> getTimeList() {
        List<Long> timeList = courseMapper.findCourseTimeList()
                .stream()
                .map(Date::getTime)
                .toList();
        log.info("获取课程时间列表成功: {}", JacksonUtil.writeValueAsString(timeList));
        return new CourseFilterDTO<>(timeList);
    }
} 