package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.errorcode.ServiceErrorCodeConstants;
import com.example.coursesystem.common.exception.ServiceException;
import com.example.coursesystem.controller.param.CourseAddParam;
import com.example.coursesystem.controller.param.CourseUpdateParam;
import com.example.coursesystem.dao.mapper.CourseMapper;
import com.example.coursesystem.dao.mapper.CourseSelectionDao;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.service.CourseService;
import com.example.coursesystem.service.dto.CourseDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CourseServiceImpl implements CourseService {

    @Autowired
    private CourseMapper courseMapper;

    @Override
    @Transactional
    public Long addCourse(CourseAddParam param) {
        CourseDO course = new CourseDO();
        BeanUtils.copyProperties(param, course);
        courseMapper.insert(course);
        return course.getId();
    }

    @Override
    @Transactional
    public Boolean updateCourse(CourseUpdateParam param) {
        CourseDO course = courseMapper.findById(param.getId());
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        BeanUtils.copyProperties(param, course);
        if (courseMapper.update(course) == 0) {
            throw new ServiceException(ServiceErrorCodeConstants.UPDATE_COURSE_ERROR);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean deleteCourse(Long id) {
        CourseDO course = courseMapper.findById(id);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        if(courseMapper.deleteById(id) == 0){
            throw new ServiceException(ServiceErrorCodeConstants.DELETE_COURSE_ERROR);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean batchDeleteCourses(List<Long> ids) {
        ids.forEach(this::deleteCourse);
        return true;
    }

    @Override
    public List<CourseDTO> listCourses() {
        return courseMapper.findAll().stream().map(course -> {
            CourseDTO result = new CourseDTO();
            BeanUtils.copyProperties(course, result);
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public CourseDTO getCourse(Long id) {
        CourseDO course = courseMapper.findById(id);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }
        CourseDTO courseDTO = new CourseDTO();
        BeanUtils.copyProperties(course, courseDTO);
        return courseDTO;
    }
} 