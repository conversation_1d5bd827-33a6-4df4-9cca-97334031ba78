package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.errorcode.ServiceErrorCodeConstants;
import com.example.coursesystem.common.exception.ServiceException;
import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.dao.dataobject.UserDO;
import com.example.coursesystem.dao.mapper.CourseMapper;
import com.example.coursesystem.dao.mapper.CourseSelectionDao;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.dao.dataobject.CourseSelectionDO;
import com.example.coursesystem.dao.mapper.UserMapper;
import com.example.coursesystem.service.CourseSelectionService;
import com.example.coursesystem.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CourseSelectionServiceImpl implements CourseSelectionService {

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CourseSelectionDao courseSelectionDao;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public void selectCourse(Long courseId) {
        // 获取当前用户
        Long userId = userService.getCurrentUserId();
        UserDO user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException(ServiceErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查课程是否存在
        CourseDO course = courseMapper.findById(courseId);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }

        // 检查是否已经选过这门课
        List<CourseSelectionDO> existingSelections = courseSelectionDao.findByUserId(userId);
        if (existingSelections.stream().anyMatch(selection -> selection.getCourseId().equals(courseId))) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_SELECTION_EXISTS);
        }

        // 检查课程容量
        if (course.getSelectedCount() >= course.getCapacity()) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_CAPACITY_FULL);
        }

        // 创建选课记录
        CourseSelectionDO courseSelection = new CourseSelectionDO();
        courseSelection.setUserId(userId);
        courseSelection.setCourseId(courseId);
        courseSelectionDao.insert(courseSelection);

        // 更新课程已选人数
        courseMapper.incrementSelectedCount(courseId);
    }

    @Override
    @Transactional
    public void dropCourse(Long courseId) {
        Long userId = userService.getCurrentUserId();
        
        // 检查选课记录是否存在
        List<CourseSelectionDO> selections = courseSelectionDao.findByUserId(userId);
        CourseSelectionDO selection = selections.stream()
                .filter(s -> s.getCourseId().equals(courseId))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND));

        // 删除选课记录
        courseSelectionDao.delete(userId, courseId);

        // 更新课程已选人数
        courseMapper.decrementSelectedCount(courseId);
    }

    @Override
    public List<CourseResult> getMyCourses() {
        Long userId = userService.getCurrentUserId();
        return courseSelectionDao.findByUserId(userId).stream()
                .map(selection -> {
                    CourseDO course = courseMapper.findById(selection.getCourseId());
                    CourseResult result = new CourseResult();
                    BeanUtils.copyProperties(course, result);
                    return result;
                })
                .collect(Collectors.toList());
    }
} 