package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.errorcode.ServiceErrorCodeConstants;
import com.example.coursesystem.common.exception.ServiceException;
import com.example.coursesystem.controller.result.CourseResult;
import com.example.coursesystem.dao.dataobject.UserDO;
import com.example.coursesystem.dao.mapper.CourseMapper;
import com.example.coursesystem.dao.mapper.CourseSelectionMapper;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.dao.dataobject.CourseSelectionDO;
import com.example.coursesystem.dao.mapper.UserMapper;
import com.example.coursesystem.service.CourseSelectionService;
import com.example.coursesystem.service.UserService;
import com.example.coursesystem.service.dto.CourseScheduleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CourseSelectionServiceImpl implements CourseSelectionService {

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CourseSelectionMapper courseSelectionMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public void selectCourse(Long courseId) {
        // 获取当前用户
        Long userId = userService.getCurrentUserId();
        UserDO user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException(ServiceErrorCodeConstants.USER_NOT_EXIST);
        }

        // 检查课程是否存在
        CourseDO course = courseMapper.findById(courseId);
        if (course == null) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND);
        }

        // 检查是否已经选过这门课
        List<CourseSelectionDO> existingSelections = courseSelectionMapper.findByUserId(userId);
        if (existingSelections.stream().anyMatch(selection -> selection.getCourseId().equals(courseId))) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_SELECTION_EXISTS);
        }

        // 检查课程容量
        if (course.getSelectedCount() >= course.getCapacity()) {
            throw new ServiceException(ServiceErrorCodeConstants.COURSE_CAPACITY_FULL);
        }

        // 创建选课记录
        CourseSelectionDO courseSelection = new CourseSelectionDO();
        courseSelection.setUserId(userId);
        courseSelection.setCourseId(courseId);
        courseSelectionMapper.insert(courseSelection);

        // 更新课程已选人数
        courseMapper.incrementSelectedCount(courseId);
    }

    @Override
    @Transactional
    public void dropCourse(Long courseId) {
        Long userId = userService.getCurrentUserId();
        
        // 检查选课记录是否存在
        List<CourseSelectionDO> selections = courseSelectionMapper.findByUserId(userId);
        CourseSelectionDO selection = selections.stream()
                .filter(s -> s.getCourseId().equals(courseId))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ServiceErrorCodeConstants.COURSE_NOT_FOUND));

        // 删除选课记录
        courseSelectionMapper.delete(userId, courseId);

        // 更新课程已选人数
        courseMapper.decrementSelectedCount(courseId);
    }

    @Override
    public List<CourseScheduleDTO> getSchedule() {
        // 查询选课信息
        Long userId = userService.getCurrentUserId();
        List<CourseScheduleDTO> collect = courseSelectionMapper.findByUserId(userId).stream()
                .map(selection -> {
                    CourseDO course = courseMapper.findById(selection.getCourseId());
                    CourseScheduleDTO result = new CourseScheduleDTO();
                    BeanUtils.copyProperties(course, result);
                    result.setTime(selection.getTime().getTime());
                    return result;
                })
                .collect(Collectors.toList());
        log.info("获取课表成功: {}", collect);
        return collect;
    }

    @Override
    public Integer getCourseSelectCount(Long courseId) {
        return courseSelectionMapper.countByCourseId(courseId);
    }
} 