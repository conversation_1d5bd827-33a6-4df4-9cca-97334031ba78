package com.example.coursesystem.service.impl;

import com.example.coursesystem.common.constant.UserLoginConstants;
import com.example.coursesystem.common.errorcode.ServiceErrorCodeConstants;
import com.example.coursesystem.common.exception.ServiceException;
import com.example.coursesystem.common.util.JWTUtil;
import com.example.coursesystem.common.util.RegexUtil;
import com.example.coursesystem.common.util.SecurityUtil;
import com.example.coursesystem.controller.param.UserPasswordLoginParam;
import com.example.coursesystem.controller.param.UserRegisterParam;
import com.example.coursesystem.dao.dataobject.UserDO;
import com.example.coursesystem.dao.mapper.UserMapper;
import com.example.coursesystem.service.UserService;
import com.example.coursesystem.service.dto.UserLoginDTO;
import com.example.coursesystem.service.dto.UserRegisterDTO;
import com.example.coursesystem.service.enums.UserIdentityEnum;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Objects;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private HttpServletRequest request;


    @Override
    public UserRegisterDTO register(UserRegisterParam param) {
        checkUserRegisterParam(param);

        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(param, userDO);
        if(StringUtils.hasText(param.getPassword())) {
            userDO.setPassword(SecurityUtil.encryptPassword(param.getPassword()));
        }
        userDO.setIdentity(Objects.requireNonNull(UserIdentityEnum.getIdentity(param.getIdentity())).name());
        userMapper.insertUser(userDO);

        return new UserRegisterDTO(userDO.getId());
    }

    @Override
    public UserLoginDTO loginByPassword(UserPasswordLoginParam param) {
        checkUserPasswordLoginParam(param);
        // 把加密后的密码, 从数据库中获取出来
        UserDO userDO = userMapper.selectByIdentifier(param.getIdentifier());
        // 查看用户是否存在
        if(null == userDO){
            throw new ServiceException(ServiceErrorCodeConstants.USER_NOT_EXIST);
        }
        // 查看两个密码是否相同
        if(!SecurityUtil.checkPassword(param.getPassword(), userDO.getPassword())){
            throw new ServiceException(ServiceErrorCodeConstants.PASSWORD_ERROR);
        }
        // 相同的话, 生成 DTO 并返回
        return convertToUserLoginDTO(userDO);
    }

    @Override
    public Long getCurrentUserId() {
        String authorization = request.getHeader(UserLoginConstants.USER_TOKEN_HEADER);
        if(!StringUtils.hasText(authorization)){
            throw new ServiceException(ServiceErrorCodeConstants.USER_NOT_LOGIN);
        }
        return JWTUtil.getUserId(authorization);
    }


    private UserLoginDTO convertToUserLoginDTO(UserDO userDO) {
        HashMap<String, Object> map = new HashMap<>();
        map.put(UserLoginConstants.USER_TOKEN_MAP_ID, userDO.getId());
        String jwt = JWTUtil.createJWT(map);
        UserLoginDTO userLoginDTO = new UserLoginDTO();
        userLoginDTO.setToken(jwt);
        BeanUtils.copyProperties(userDO, userLoginDTO);
        return userLoginDTO;
    }

    private void checkUserPasswordLoginParam(UserPasswordLoginParam param) {
        if(null == param){
            throw new ServiceException(ServiceErrorCodeConstants.LOGIN_PARAM_IS_NULL);
        }
        if(!StringUtils.hasText(param.getIdentifier())){
            throw new ServiceException(ServiceErrorCodeConstants.USER_IDENTIFIER_EMPTY_ERROR);
        }
        if(!StringUtils.hasText(param.getPassword())){
            throw new ServiceException(ServiceErrorCodeConstants.PASSWORD_EMPTY_ERROR);
        }
    }

    private void checkUserRegisterParam(UserRegisterParam param) {
        if(null == param){
            throw new ServiceException(ServiceErrorCodeConstants.USER_REGISTER_PARAM_IS_NULL);
        }
        // 校验邮箱格式
        if(!RegexUtil.isEmail(param.getEmail())){
            throw new ServiceException(ServiceErrorCodeConstants.EMAIL_FORMAT_ERROR);
        }
        // 校验手机号格式
        if(!RegexUtil.isPhoneNumber(param.getPhoneNumber())){
            throw new ServiceException(ServiceErrorCodeConstants.PHONE_NUMBER_FORMAT_ERROR);
        }
        // 校验身份信息, 这里只有两种, 采用枚举
        if(null == UserIdentityEnum.getIdentity(param.getIdentity())){
            throw new ServiceException(ServiceErrorCodeConstants.USER_IDENTITY_ERROR);
        }
        // 校验密码必填
        if(!StringUtils.hasText(param.getPassword())){
            throw new ServiceException(ServiceErrorCodeConstants.PASSWORD_EMPTY_ERROR);
        }

        // 如果密码不为空, 那么校验密码格式, 此处设为至少 6 位
        if(StringUtils.hasText(param.getPassword())
                && !RegexUtil.isPassword(param.getPassword())){
            throw new ServiceException(ServiceErrorCodeConstants.PASSWORD_FORMAT_ERROR);
        }
        // 校验邮箱是否被使用
        if(checkEmailUsed(param.getEmail())){
            throw new ServiceException(ServiceErrorCodeConstants.EMAIL_EXIST_ERROR);
        }
        // 校验手机号是否被使用
        if(checkPhoneNumberUsed(param.getPhoneNumber())){
            throw new ServiceException(ServiceErrorCodeConstants.PHONE_NUMBER_EXIST_ERROR);
        }
    }

    private boolean checkPhoneNumberUsed(@NotBlank(message = "手机号不能为空") String phoneNumber) {
        return userMapper.countByPhoneNumber(phoneNumber) > 0;
    }

    private boolean checkEmailUsed(@NotBlank(message = "邮箱不能为空") String email) {
        return userMapper.countByMail(email) > 0;
    }
}
