package com.example.coursesystem.service;

import com.example.coursesystem.controller.param.UserPasswordLoginParam;
import com.example.coursesystem.controller.param.UserRegisterParam;
import com.example.coursesystem.service.dto.UserLoginDTO;
import com.example.coursesystem.service.dto.UserRegisterDTO;


public interface UserService {

    UserRegisterDTO register(UserRegisterParam userRegisterParam);

    UserLoginDTO loginByPassword(UserPasswordLoginParam userPasswordLoginParam);

    Long getCurrentUserId();
}
