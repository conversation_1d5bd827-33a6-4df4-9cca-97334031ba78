package com.example.coursesystem.service;

import com.example.coursesystem.controller.param.CourseAddParam;
import com.example.coursesystem.controller.param.CourseListParam;
import com.example.coursesystem.controller.param.CourseUpdateParam;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.service.dto.CourseDTO;
import com.example.coursesystem.service.dto.CourseFilterDTO;

import java.util.Date;
import java.util.List;

public interface CourseService {
    Long addCourse(CourseAddParam param);
    Boolean updateCourse(CourseUpdateParam param);
    Boolean deleteCourse(Long id);
    Boolean batchDeleteCourses(List<Long> ids);
    List<CourseDTO> listCourses(CourseListParam param);
    CourseDTO getCourse(Long id);
    CourseFilterDTO<String> getTeacherList();
    CourseFilterDTO<Long> getTimeList();
}