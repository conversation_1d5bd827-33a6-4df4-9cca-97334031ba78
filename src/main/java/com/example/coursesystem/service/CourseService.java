package com.example.coursesystem.service;

import com.example.coursesystem.controller.param.CourseAddParam;
import com.example.coursesystem.controller.param.CourseUpdateParam;
import com.example.coursesystem.dao.dataobject.CourseDO;
import com.example.coursesystem.service.dto.CourseDTO;

import java.util.List;

public interface CourseService {
    Long addCourse(CourseAddParam param);
    Boolean updateCourse(CourseUpdateParam param);
    Boolean deleteCourse(Long id);
    Boolean batchDeleteCourses(List<Long> ids);
    List<CourseDTO> listCourses();
    CourseDTO getCourse(Long id);
}