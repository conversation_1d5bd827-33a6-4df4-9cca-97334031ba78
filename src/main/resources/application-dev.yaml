spring:
  datasource:
    url: *****************************************************************************
    username: root
    password: 1234
    driver-class-name: com.mysql.cj.jdbc.Driver
  mvc:
    favicon:
      enable: false

# 设置 Mybatis 的 xml 保存路径
mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml
  configuration: # 配置打印 MyBatis 执行的 SQL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true  #自动驼峰转换
# 配置打印 MyBatis 执行的 SQL
logging:
  file:
    name: logs/springboot.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      file-name-pattern: ${LOG_FILE}.%d{yyyy-MM-dd}.%i
  level:
    com:
      example:
        demo: debug

jwt:
  secret-key: t0Sm4Y3NafYWIqe+/fzL94h/xSL4nkMgzG7nc6ejMl0=
  expire-time: 604800000  # 1000 * 60 * 60 * 24 * 7, 即一周