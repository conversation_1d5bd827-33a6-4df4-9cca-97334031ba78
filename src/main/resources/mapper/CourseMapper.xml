<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.coursesystem.dao.mapper.CourseMapper">

    <select id="findAll" resultType="com.example.coursesystem.dao.dataobject.CourseDO">
        select * from course
        <where>
            <if test="search != null and search != ''">
                and course_name like concat('%', #{search}, '%')
            </if>
            <if test="teacher != null and teacher != ''">
                and teacher = #{teacher}
            </if>
            <if test="time != null">
                and time = #{time}
            </if>
        </where>
    </select>
</mapper>