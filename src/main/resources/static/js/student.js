// 检查登录状态
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('user'));
    console.log(user)
    if (!user || user.identity !== 'STUDENT') {
        window.location.href = '/index.html';
        return;
    }
    document.getElementById('studentName').textContent = user.name;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    loadFilterOptions(); // 先加载过滤器选项
    loadCourses();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 视图切换
    document.querySelectorAll('[data-view]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const view = e.target.dataset.view;
            switchView(view);
        });
    });

    // 搜索和筛选
    document.getElementById('searchInput').addEventListener('input', debounce(loadCourses, 300));
    document.getElementById('teacherFilter').addEventListener('change', loadCourses);
    document.getElementById('timeFilter').addEventListener('change', loadCourses);
    document.getElementById('sortBy').addEventListener('change', loadCourses);

    // 添加刷新过滤器按钮的事件监听器（如果需要的话）
    const refreshFiltersBtn = document.getElementById('refreshFilters');
    if (refreshFiltersBtn) {
        refreshFiltersBtn.addEventListener('click', loadFilterOptions);
    }
}

// 切换视图
function switchView(view) {
    document.querySelectorAll('[data-view]').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    document.getElementById('courseListView').classList.toggle('d-none', view !== 'course-list');
    document.getElementById('scheduleView').classList.toggle('d-none', view !== 'schedule');

    if (view === 'schedule') {
        loadSchedule();
    }
}

// 加载过滤器选项
async function loadFilterOptions() {
    try {
        // 加载教师列表
        const teachersResponse = await fetch('/course/filter/teacher', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (teachersResponse.ok) {
            const teachersData = await teachersResponse.json();
            if (teachersData.code === 200) {
                updateTeacherFilter(teachersData.data.filterData);
            }
        }

        // 加载时间段列表
        const timesResponse = await fetch('/course/filter/time', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (timesResponse.ok) {
            const timesData = await timesResponse.json();
            if (timesData.code === 200) {
                updateTimeFilter(timesData.data.filterData);
            }
        }
    } catch (error) {
        console.warn('加载过滤器选项失败:', error);
        // 如果后端接口不可用，保持原有的前端过滤逻辑作为备选
    }
}

// 更新教师过滤器
function updateTeacherFilter(teachers) {
    const teacherFilter = document.getElementById('teacherFilter');
    teacherFilter.innerHTML = '<option value="">选择教师</option>' +
        teachers.map(teacher => `<option value="${teacher}">${teacher}</option>`).join('');
}

// 更新时间过滤器
function updateTimeFilter(times) {
    const timeFilter = document.getElementById('timeFilter');
    timeFilter.innerHTML = '<option value="">选择上课时间</option>' +
        times.map(time => `<option value="${time}">${getFormatTime(time)}</option>`).join('');
}

// 加载课程列表
async function loadCourses() {
    showLoading();
    const searchQuery = document.getElementById('searchInput').value;
    const teacher = document.getElementById('teacherFilter').value;
    const timeTimestamp = document.getElementById('timeFilter').value; // 现在是时间戳
    const sortBy = document.getElementById('sortBy').value;

    // 构建查询参数
    const params = new URLSearchParams();
    if (searchQuery) params.append('search', searchQuery);
    if (teacher) params.append('teacher', teacher);
    if (timeTimestamp) params.append('time', timeTimestamp); // 传递时间戳
    if (sortBy) params.append('sort', sortBy);

    try {
        const response = await fetch(`/course/list?${params.toString()}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课程失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message);

        renderCourses(data.data);
        // 移除原有的updateFilters调用，因为现在过滤器选项来自后端
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课程卡片
function renderCourses(courses) {
    const grid = document.getElementById('courseGrid');
    grid.innerHTML = '';

    courses.forEach(course => {
        // 计算剩余名额
        const remainingCapacity = course.capacity - course.selectedCount;

        const card = document.createElement('div');
        card.className = 'col';
        card.innerHTML = `
            <div class="card course-card h-100">
                <div class="card-body">
                    <h5 class="card-title">${course.courseName}</h5>
                    <p class="card-text">
                        <small class="text-muted">教师：${course.teacher}</small><br>
                        <small class="text-muted">时间：${getFormatTime(course.time)}</small><br>
                        <small class="text-muted">教室：${course.classroom}</small>
                    </p>
                    <p class="card-text">
                        剩余名额：${remainingCapacity}/${course.capacity}
                    </p>
                </div>
                <div class="card-footer bg-transparent">
                    ${course.selected ?
            `<button class="btn btn-danger w-100" onclick="dropCourse(${course.id})">退课</button>` :
            `<button class="btn btn-primary w-100" onclick="selectCourse(${course.id})" ${remainingCapacity === 0 ? 'disabled' : ''}>选课</button>`
        }
                </div>
            </div>
        `;
        grid.appendChild(card);
    });
}

// 选课
async function selectCourse(courseId) {
    showLoading();
    try {
        const response = await fetch('/course-selection/select/' + courseId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        const data = await response.json();
        if (!response.ok || data.code !== 200) throw new Error(data.message || '选课失败');

        showToast('选课成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 退课
async function dropCourse(courseId) {
    if (!confirm('确定要退选该课程吗？')) return;

    showLoading();
    try {
        const response = await fetch('/course-selection/drop/' + courseId,{
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
        });

        const data = await response.json();
        if (!response.ok || data.code !== 200) throw new Error(data.message || '退课失败');

        showToast('退课成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 加载课表
async function loadSchedule() {
    showLoading();
    try {
        const response = await fetch('/course-selection/schedule', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课表失败');

        const data = await response.json();
        if(data.code !== 200) throw new Error(data.message);

        renderSchedule(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课表
function renderSchedule(schedule) {
    // 只选择课程内容单元格，不包括时间列
    const cells = document.querySelectorAll('.schedule-cell:not(.header)');

    // 只清空课程内容单元格，保留时间列的内容
    cells.forEach((cell, index) => {
        // 跳过时间列（每行的第一个单元格：索引 0, 6, 12, 18）
        if (index % 6 !== 0) {
            cell.innerHTML = '';
            cell.classList.remove('selected');
        }
    });

    schedule.forEach((course, index) => {
        // 将时间戳转换为Date对象
        const courseDate = new Date(course.time);

        // 获取星期几 (0=周日, 1=周一, ..., 6=周六)
        const dayOfWeek = courseDate.getDay();

        // 获取小时 (使用本地时间)
        const hour = courseDate.getHours();

        // 将星期几转换为对应的字符串
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const day = dayNames[dayOfWeek];

        // 根据小时确定时间段，与getCellIndex中的时间格式保持一致
        let timeSlot = '';
        if (hour >= 8 && hour < 10) {
            timeSlot = '8:00-10:00';
        } else if (hour >= 10 && hour < 12) {
            timeSlot = '10:15-12:15';
        } else if (hour >= 14 && hour < 16) {
            timeSlot = '14:00-16:00';
        } else if (hour >= 16 && hour < 18) {
            timeSlot = '16:15-18:15';
        }

        const cellIndex = getCellIndex(day, timeSlot);

        if (cellIndex !== -1 && cellIndex < cells.length) {
            const cell = cells[cellIndex];
            if (cell) {
                cell.innerHTML = `
                    <div class="course-info">
                        <strong>${course.courseName}</strong><br>
                        <small>${course.teacher}</small><br>
                        <small>${course.classroom}</small>
                    </div>
                `;
                cell.classList.add('selected');
            }
        }
    });
}

// 获取单元格索引
function getCellIndex(day, time) {
    const days = ['周一', '周二', '周三', '周四', '周五'];
    const times = ['8:00-10:00', '10:15-12:15', '14:00-16:00', '16:15-18:15'];

    const dayIndex = days.indexOf(day);
    const timeIndex = times.indexOf(time);

    if (dayIndex === -1 || timeIndex === -1) {
        return -1;
    }

    // 计算单元格索引：(时间行索引 + 1) * 6 + 星期列索引 + 1
    // +1 是因为要跳过表头行和时间列
    return (timeIndex + 1) * 6 + dayIndex + 1;
}

// 显示加载动画
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 退出登录
function logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/index.html';
}

// 时间格式化函数 - 将时间戳转换为可读的时间格式
function getFormatTime(timestamp) {
    if (!timestamp) return '';

    // 创建 Date 对象
    const date = new Date(timestamp);

    // 检查日期是否有效
    if (isNaN(date.getTime())) return '';

    // 提取各时间部分
    const year = date.getFullYear();
    const month = date.getMonth() + 1;    // 注意月份是0~11
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();

    // 手动拼接成格式化字符串 (YYYY-MM-DD HH:mm)
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// 获取时间的简短格式 (仅用于显示时间段)
function getTimeSlot(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';

    const hours = date.getHours();
    const minutes = date.getMinutes();

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}