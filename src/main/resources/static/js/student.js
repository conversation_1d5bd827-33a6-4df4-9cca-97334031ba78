// 检查登录状态
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('user'));
    console.log(user)
    if (!user || user.role !== 'STUDENT') {
        // window.location.href = '/index.html';
        return;
    }
    document.getElementById('studentName').textContent = user.name;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    loadCourses();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 视图切换
    document.querySelectorAll('[data-view]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const view = e.target.dataset.view;
            switchView(view);
        });
    });

    // 搜索和筛选
    document.getElementById('searchInput').addEventListener('input', debounce(loadCourses, 300));
    document.getElementById('teacherFilter').addEventListener('change', loadCourses);
    document.getElementById('timeFilter').addEventListener('change', loadCourses);
    document.getElementById('sortBy').addEventListener('change', loadCourses);
}

// 切换视图
function switchView(view) {
    document.querySelectorAll('[data-view]').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    document.getElementById('courseListView').classList.toggle('d-none', view !== 'course-list');
    document.getElementById('scheduleView').classList.toggle('d-none', view !== 'schedule');

    if (view === 'schedule') {
        loadSchedule();
    }
}

// 加载课程列表
async function loadCourses() {
    showLoading();
    const searchQuery = document.getElementById('searchInput').value;
    const teacher = document.getElementById('teacherFilter').value;
    const time = document.getElementById('timeFilter').value;
    const sortBy = document.getElementById('sortBy').value;

    try {
        const response = await fetch(`/api/courses?search=${searchQuery}&teacher=${teacher}&time=${time}&sort=${sortBy}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课程失败');

        const data = await response.json();
        renderCourses(data.data);
        updateFilters(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课程卡片
function renderCourses(courses) {
    const grid = document.getElementById('courseGrid');
    grid.innerHTML = '';

    courses.forEach(course => {
        const card = document.createElement('div');
        card.className = 'col';
        card.innerHTML = `
            <div class="card course-card h-100">
                <div class="card-body">
                    <h5 class="card-title">${course.name}</h5>
                    <p class="card-text">
                        <small class="text-muted">教师：${course.teacher}</small><br>
                        <small class="text-muted">时间：${course.time}</small><br>
                        <small class="text-muted">教室：${course.classroom}</small>
                    </p>
                    <p class="card-text">
                        剩余名额：${course.remainingCapacity}/${course.capacity}
                    </p>
                </div>
                <div class="card-footer bg-transparent">
                    ${course.selected ? 
                        `<button class="btn btn-danger w-100" onclick="dropCourse(${course.id})">退课</button>` :
                        `<button class="btn btn-primary w-100" onclick="selectCourse(${course.id})" ${course.remainingCapacity === 0 ? 'disabled' : ''}>选课</button>`
                    }
                </div>
            </div>
        `;
        grid.appendChild(card);
    });
}

// 更新筛选器选项
function updateFilters(courses) {
    const teachers = [...new Set(courses.map(c => c.teacher))];
    const times = [...new Set(courses.map(c => c.time))];

    const teacherFilter = document.getElementById('teacherFilter');
    const timeFilter = document.getElementById('timeFilter');

    teacherFilter.innerHTML = '<option value="">选择教师</option>' + 
        teachers.map(t => `<option value="${t}">${t}</option>`).join('');

    timeFilter.innerHTML = '<option value="">选择上课时间</option>' +
        times.map(t => `<option value="${t}">${t}</option>`).join('');
}

// 选课
async function selectCourse(courseId) {
    showLoading();
    try {
        const response = await fetch('/api/courses/select', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ courseId })
        });

        const data = await response.json();
        if (!response.ok) throw new Error(data.message);

        showToast('选课成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 退课
async function dropCourse(courseId) {
    if (!confirm('确定要退选该课程吗？')) return;

    showLoading();
    try {
        const response = await fetch('/api/courses/drop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ courseId })
        });

        const data = await response.json();
        if (!response.ok) throw new Error(data.message);

        showToast('退课成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 加载课表
async function loadSchedule() {
    showLoading();
    try {
        const response = await fetch('/api/schedule', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课表失败');

        const data = await response.json();
        renderSchedule(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课表
function renderSchedule(schedule) {
    const cells = document.querySelectorAll('.schedule-cell:not(.header)');
    cells.forEach(cell => cell.innerHTML = '');

    schedule.forEach(course => {
        const [day, time] = course.time.split(' ');
        const cellIndex = getCellIndex(day, time);
        if (cellIndex !== -1) {
            const cell = cells[cellIndex];
            cell.innerHTML = `
                <div class="course-info">
                    <strong>${course.name}</strong><br>
                    <small>${course.teacher}</small><br>
                    <small>${course.classroom}</small>
                </div>
            `;
            cell.classList.add('selected');
        }
    });
}

// 获取单元格索引
function getCellIndex(day, time) {
    const days = ['周一', '周二', '周三', '周四', '周五'];
    const times = ['8:00-10:00', '10:15-12:15', '14:00-16:00', '16:15-18:15'];
    
    const dayIndex = days.indexOf(day);
    const timeIndex = times.indexOf(time);
    
    if (dayIndex === -1 || timeIndex === -1) return -1;
    
    return (timeIndex + 1) * 6 + dayIndex + 1;
}

// 显示加载动画
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 退出登录
function logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/index.html';
} 