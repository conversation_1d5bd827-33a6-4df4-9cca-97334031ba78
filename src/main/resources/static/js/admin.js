// 检查登录状态
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || user.identity !== 'ADMIN') {
        window.location.href = '/index.html';
        return;
    }
    document.getElementById('adminName').textContent = user.name;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    loadCourses();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 视图切换
    document.querySelectorAll('[data-view]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const view = e.target.dataset.view;
            switchView(view);
        });
    });

    // 全选/取消全选
    document.getElementById('selectAll').addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('input[name="courseSelect"]');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
    });

    // 学生搜索
    document.getElementById('studentSearch').addEventListener('input', debounce(loadStudents, 300));

    // 学生状态筛选
    document.querySelectorAll('[data-status]').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const status = e.target.dataset.status;
            loadStudents(status);
        });
    });
}

// 切换视图
function switchView(view) {
    document.querySelectorAll('[data-view]').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    document.getElementById('courseManagementView').classList.toggle('d-none', view !== 'course-management');
    document.getElementById('studentManagementView').classList.toggle('d-none', view !== 'student-management');

    if (view === 'student-management') {
        loadStudents();
    }
}

// 加载课程列表
async function loadCourses() {
    showLoading();
    try {
        const response = await fetch('/course/list', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课程失败');

        const data = await response.json();
        renderCourses(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课程列表
function renderCourses(courses) {
    const tbody = document.getElementById('courseTableBody');
    tbody.innerHTML = '';

    courses.forEach(course => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input" name="courseSelect" value="${course.id}">
            </td>
            <td>${course.id}</td>
            <td>${course.name}</td>
            <td>${course.teacher}</td>
            <td>${course.time}</td>
            <td>${course.classroom}</td>
            <td>${course.capacity}</td>
            <td>${course.selectedCount}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editCourse(${course.id})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteCourse(${course.id})">删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 加载学生列表
async function loadStudents(status = 'all') {
    showLoading();
    const searchQuery = document.getElementById('studentSearch').value;

    try {
        const response = await fetch(`/api/admin/students?search=${searchQuery}&status=${status}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载学生列表失败');

        const data = await response.json();
        renderStudents(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染学生列表
function renderStudents(students) {
    const tbody = document.getElementById('studentTableBody');
    tbody.innerHTML = '';

    students.forEach(student => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${student.studentId}</td>
            <td>${student.name}</td>
            <td>
                <span class="badge ${student.status === 'active' ? 'bg-success' : 'bg-danger'}">
                    ${student.status === 'active' ? '正常' : '已冻结'}
                </span>
            </td>
            <td>${student.courseCount}</td>
            <td>
                <div class="btn-group">
                    <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        操作
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleStudentStatus(${student.studentId}, '${student.status}')">
                                ${student.status === 'active' ? '冻结账号' : '解冻账号'}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="resetPassword(${student.studentId})">
                                重置密码
                            </a>
                        </li>
                    </ul>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 编辑课程
async function editCourse(courseId) {
    showLoading();
    try {
        const response = await fetch(`/api/admin/courses/${courseId}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('获取课程信息失败');

        const data = await response.json();
        const course = data.data;

        // 填充表单
        document.getElementById('courseId').value = course.id;
        document.getElementById('courseName').value = course.name;
        document.getElementById('teacher').value = course.teacher;
        document.getElementById('time').value = course.time;
        document.getElementById('classroom').value = course.classroom;
        document.getElementById('capacity').value = course.capacity;

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('courseModal'));
        modal.show();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 保存课程
async function saveCourse() {
    const courseId = document.getElementById('courseId').value;
    const courseData = {
        name: document.getElementById('courseName').value,
        teacher: document.getElementById('teacher').value,
        time: document.getElementById('time').value,
        classroom: document.getElementById('classroom').value,
        capacity: parseInt(document.getElementById('capacity').value)
    };

    showLoading();
    try {
        const response = await fetch(`/api/admin/courses${courseId ? `/${courseId}` : ''}`, {
            method: courseId ? 'PUT' : 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify(courseData)
        });

        if (!response.ok) throw new Error('保存课程失败');

        showToast('保存成功');
        bootstrap.Modal.getInstance(document.getElementById('courseModal')).hide();
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 删除课程
async function deleteCourse(courseId) {
    if (!confirm('确定要删除该课程吗？')) return;

    showLoading();
    try {
        const response = await fetch(`/api/admin/courses/${courseId}`, {
            method: 'DELETE',
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('删除课程失败');

        showToast('删除成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 批量删除课程
async function batchDelete() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="courseSelect"]:checked'))
        .map(checkbox => checkbox.value);

    if (selectedIds.length === 0) {
        showToast('请选择要删除的课程', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.length} 门课程吗？`)) return;

    showLoading();
    try {
        const response = await fetch('/api/admin/courses/batch', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ ids: selectedIds })
        });

        if (!response.ok) throw new Error('批量删除失败');

        showToast('删除成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 导出Excel
async function exportToExcel() {
    showLoading();
    try {
        const response = await fetch('/api/admin/courses/export', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('导出失败');

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '课程列表.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 切换学生状态
async function toggleStudentStatus(studentId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'frozen' : 'active';
    const action = newStatus === 'frozen' ? '冻结' : '解冻';

    if (!confirm(`确定要${action}该学生的账号吗？`)) return;

    showLoading();
    try {
        const response = await fetch(`/api/admin/students/${studentId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) throw new Error(`${action}账号失败`);

        showToast(`${action}成功`);
        loadStudents();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 重置学生密码
async function resetPassword(studentId) {
    if (!confirm('确定要重置该学生的密码吗？')) return;

    showLoading();
    try {
        const response = await fetch(`/api/admin/students/${studentId}/password`, {
            method: 'PUT',
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('重置密码失败');

        const data = await response.json();
        showToast(`密码已重置为：${data.data.password}`);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 显示加载动画
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 退出登录
function logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/index.html';
}

// 注册新用户
async function registerUser() {
    const userId = document.getElementById('userId').value;
    const userName = document.getElementById('userName').value;
    const password = document.getElementById('userPassword').value;
    const confirmPassword = document.getElementById('confirmUserPassword').value;
    const role = document.getElementById('userRole').value;
    const errorElement = document.getElementById('registerUserError');

    // 密码一致性检查
    if (password !== confirmPassword) {
        errorElement.textContent = '两次输入的密码不一致';
        errorElement.classList.remove('d-none');
        return;
    }

    showLoading();
    try {
        const response = await fetch('/api/admin/users/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                userId,
                name: userName,
                password,
                role
            })
        });

        const data = await response.json();
        if (!response.ok) throw new Error(data.message);

        showToast('注册成功');
        bootstrap.Modal.getInstance(document.getElementById('registerUserModal')).hide();
        document.getElementById('registerUserForm').reset();
        loadStudents();
    } catch (error) {
        errorElement.textContent = error.message;
        errorElement.classList.remove('d-none');
    } finally {
        hideLoading();
    }
}

// 密码一致性实时检查
document.getElementById('confirmUserPassword').addEventListener('input', function() {
    const password = document.getElementById('userPassword').value;
    const confirmPassword = this.value;
    const errorElement = document.getElementById('registerUserError');

    if (password !== confirmPassword) {
        errorElement.textContent = '两次输入的密码不一致';
        errorElement.classList.remove('d-none');
    } else {
        errorElement.classList.add('d-none');
    }
}); 