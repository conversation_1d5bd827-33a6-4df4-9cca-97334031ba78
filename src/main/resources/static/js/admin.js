// 检查登录状态
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || user.identity !== 'ADMIN') {
        window.location.href = '/index.html';
        return;
    }
    document.getElementById('adminName').textContent = user.username || user.name;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    loadCourses();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 视图切换
    document.querySelectorAll('[data-view]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const view = e.target.dataset.view;
            switchView(view);
        });
    });

    // 全选/取消全选
    document.getElementById('selectAll').addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('input[name="courseSelect"]');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
    });

    // 学生搜索
    document.getElementById('studentSearch').addEventListener('input', debounce(() => loadStudents(), 300));

    // 学生状态筛选
    document.querySelectorAll('[data-status]').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const status = e.target.dataset.status;
            loadStudents(status);
        });
    });
}

// 切换视图
function switchView(view) {
    document.querySelectorAll('[data-view]').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    document.getElementById('courseManagementView').classList.toggle('d-none', view !== 'course-management');
    document.getElementById('studentManagementView').classList.toggle('d-none', view !== 'student-management');

    if (view === 'student-management') {
        loadStudents();
    }
}

// 加载课程列表
async function loadCourses() {
    showLoading();
    try {
        const response = await fetch('/course/list', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载课程失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '加载课程失败');

        renderCourses(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染课程列表
function renderCourses(courses) {
    const tbody = document.getElementById('courseTableBody');
    tbody.innerHTML = '';

    courses.forEach(course => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input" name="courseSelect" value="${course.id}">
            </td>
            <td>${course.id}</td>
            <td>${course.courseName}</td>
            <td>${course.teacher}</td>
            <td>${getFormatTime ? getFormatTime(course.time) : course.time}</td>
            <td>${course.classroom}</td>
            <td>${course.capacity}</td>
            <td>${course.selectedCount}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editCourse(${course.id})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteCourse(${course.id})">删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 加载学生列表
async function loadStudents(status = 'all') {
    showLoading();
    const searchQuery = document.getElementById('studentSearch').value;

    try {
        const response = await fetch(`/admin/student?search=${searchQuery}&status=${status}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('加载学生列表失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '加载学生列表失败');

        renderStudents(data.data);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 渲染学生列表
function renderStudents(students) {
    const tbody = document.getElementById('studentTableBody');
    tbody.innerHTML = '';

    students.forEach(student => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${student.studentId}</td>
            <td>${student.name}</td>
            <td>
                <span class="badge ${student.status === 'active' ? 'bg-success' : 'bg-danger'}">
                    ${student.status === 'active' ? '正常' : '已冻结'}
                </span>
            </td>
            <td>${student.courseCount}</td>
            <td>
                <div class="btn-group">
                    <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        操作
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleStudentStatus(${student.studentId}, '${student.status}')">
                                ${student.status === 'active' ? '冻结账号' : '解冻账号'}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="resetPassword(${student.studentId})">
                                重置密码
                            </a>
                        </li>
                    </ul>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 编辑课程
async function editCourse(courseId) {
    showLoading();
    try {
        const response = await fetch(`/course/get/${courseId}`, {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('获取课程信息失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '获取课程信息失败');

        const course = data.data;

        // 填充表单
        document.getElementById('courseId').value = course.id;
        document.getElementById('courseName').value = course.courseName || course.name;
        document.getElementById('teacher').value = course.teacher;

        // 将时间戳转换为选择器格式
        if (course.time) {
            const timeText = convertTimestampToSelectOption(course.time);
            document.getElementById('time').value = timeText;
        }

        document.getElementById('classroom').value = course.classroom;
        document.getElementById('capacity').value = course.capacity;

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('courseModal'));
        modal.show();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 保存课程
async function saveCourse() {
    const courseId = document.getElementById('courseId').value;
    const timeValue = document.getElementById('time').value;

    // 根据是否有courseId构建不同的数据结构
    let courseData, endpoint, method;

    if (courseId) {
        // 更新课程
        courseData = {
            id: parseInt(courseId),
            courseName: document.getElementById('courseName').value,
            teacher: document.getElementById('teacher').value,
            time: timeValue ? convertSelectOptionToTimestamp(timeValue) : null,
            classroom: document.getElementById('classroom').value,
            capacity: parseInt(document.getElementById('capacity').value)
        };
        endpoint = '/course/update';
        method = 'POST';
    } else {
        // 添加课程
        courseData = {
            courseName: document.getElementById('courseName').value,
            teacher: document.getElementById('teacher').value,
            time: timeValue ? convertSelectOptionToTimestamp(timeValue) : null,
            classroom: document.getElementById('classroom').value,
            capacity: parseInt(document.getElementById('capacity').value)
        };
        endpoint = '/course/add';
        method = 'POST';
    }

    showLoading();
    try {
        const response = await fetch(endpoint, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify(courseData)
        });

        if (!response.ok) throw new Error('保存课程失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '保存课程失败');

        showToast(courseId ? '更新成功' : '添加成功');
        bootstrap.Modal.getInstance(document.getElementById('courseModal')).hide();
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 删除课程
async function deleteCourse(courseId) {
    if (!confirm('确定要删除该课程吗？')) return;

    showLoading();
    try {
        const response = await fetch(`/course/delete/${courseId}`, {
            method: 'POST',
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('删除课程失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '删除课程失败');

        showToast('删除成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 批量删除课程
async function batchDelete() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="courseSelect"]:checked'))
        .map(checkbox => checkbox.value);

    if (selectedIds.length === 0) {
        showToast('请选择要删除的课程', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.length} 门课程吗？`)) return;

    showLoading();
    try {
        const response = await fetch('/course/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ courseIds: selectedIds })
        });

        if (!response.ok) throw new Error('批量删除失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '批量删除失败');

        showToast('删除成功');
        loadCourses();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 导出Excel
async function exportToExcel() {
    showLoading();
    try {
        const response = await fetch('/course/export', {
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('导出失败');

        // 对于文件下载，通常不需要检查JSON响应码
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '课程列表.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showToast('导出成功');
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 切换学生状态
async function toggleStudentStatus(studentId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'frozen' : 'active';
    const action = newStatus === 'frozen' ? '冻结' : '解冻';

    if (!confirm(`确定要${action}该学生的账号吗？`)) return;

    showLoading();
    try {
        const response = await fetch(`/api/admin/students/${studentId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) throw new Error(`${action}账号失败`);

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || `${action}账号失败`);

        showToast(`${action}成功`);
        loadStudents();
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 重置学生密码
async function resetPassword(studentId) {
    if (!confirm('确定要重置该学生的密码吗？')) return;

    showLoading();
    try {
        const response = await fetch(`/api/admin/students/${studentId}/password`, {
            method: 'PUT',
            headers: {
                'user_token': `${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) throw new Error('重置密码失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '重置密码失败');

        showToast(`密码已重置为：${data.data.password}`);
    } catch (error) {
        showToast(error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 显示加载动画
function showLoading() {
    document.getElementById('loadingSpinner').classList.remove('d-none');
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loadingSpinner').classList.add('d-none');
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 退出登录
function logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/index.html';
}

// 时间格式化函数 - 将时间戳转换为可读的时间格式
function getFormatTime(timestamp) {
    if (!timestamp) return '';

    // 创建 Date 对象
    const date = new Date(timestamp);

    // 检查日期是否有效
    if (isNaN(date.getTime())) return '';

    // 提取各时间部分
    const year = date.getFullYear();
    const month = date.getMonth() + 1;    // 注意月份是0~11
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();

    // 手动拼接成格式化字符串 (YYYY-MM-DD HH:mm)
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// 将时间戳转换为选择器选项格式 (如: "周一 8:00-10:00")
function convertTimestampToSelectOption(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';

    // 获取星期几
    const dayOfWeek = date.getDay();
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const dayName = dayNames[dayOfWeek];

    // 获取小时
    const hour = date.getHours();

    // 根据小时确定时间段
    let timeSlot = '';
    if (hour >= 8 && hour < 10) {
        timeSlot = '8:00-10:00';
    } else if (hour >= 10 && hour < 12) {
        timeSlot = '10:15-12:15';
    } else if (hour >= 14 && hour < 16) {
        timeSlot = '14:00-16:00';
    } else if (hour >= 16 && hour < 18) {
        timeSlot = '16:15-18:15';
    }

    return timeSlot ? `${dayName} ${timeSlot}` : '';
}

// 将选择器选项格式转换为时间戳 (如: "周一 8:00-10:00" -> timestamp)
function convertSelectOptionToTimestamp(selectValue) {
    if (!selectValue) return null;

    // 解析选择器值 (如: "周一 8:00-10:00")
    const parts = selectValue.split(' ');
    if (parts.length !== 2) return null;

    const dayName = parts[0];
    const timeRange = parts[1];

    // 星期映射
    const dayMap = {
        '周一': 1, '周二': 2, '周三': 3, '周四': 4, '周五': 5, '周六': 6, '周日': 0
    };

    const targetDay = dayMap[dayName];
    if (targetDay === undefined) return null;

    // 解析时间 (如: "8:00-10:00")
    const startTime = timeRange.split('-')[0];
    const [hours, minutes] = startTime.split(':').map(Number);

    // 创建下周的日期 (避免使用过去的日期)
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    // 找到下周对应的星期几
    const daysUntilTarget = (targetDay + 7 - nextWeek.getDay()) % 7;
    const targetDate = new Date(nextWeek.getTime() + daysUntilTarget * 24 * 60 * 60 * 1000);

    // 设置具体时间
    targetDate.setHours(hours, minutes, 0, 0);

    return targetDate.getTime();
}

// 注册新用户
async function registerUser() {
    const userId = document.getElementById('userId').value;
    const userName = document.getElementById('userName').value;
    const password = document.getElementById('userPassword').value;
    const confirmPassword = document.getElementById('confirmUserPassword').value;
    const email = document.getElementById('userEmail').value;
    const phoneNumber = document.getElementById('userPhoneNumber').value;
    const identity = document.getElementById('userRole').value;
    const errorElement = document.getElementById('registerUserError');

    // 清除之前的错误信息
    errorElement.classList.add('d-none');

    // 表单验证
    if (password !== confirmPassword) {
        errorElement.textContent = '两次输入的密码不一致';
        errorElement.classList.remove('d-none');
        return;
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phoneNumber)) {
        errorElement.textContent = '请输入正确的手机号格式';
        errorElement.classList.remove('d-none');
        return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        errorElement.textContent = '请输入正确的邮箱格式';
        errorElement.classList.remove('d-none');
        return;
    }

    showLoading();
    try {
        const response = await fetch('/user/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'user_token': `${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                userId,
                username: userName,
                password,
                identity,
                email,
                phoneNumber
            })
        });

        if (!response.ok) throw new Error('注册失败');

        const data = await response.json();
        if (data.code !== 200) throw new Error(data.message || '注册失败');

        showToast('注册成功');
        bootstrap.Modal.getInstance(document.getElementById('registerUserModal')).hide();
        document.getElementById('registerUserForm').reset();
        loadStudents();
    } catch (error) {
        errorElement.textContent = error.message;
        errorElement.classList.remove('d-none');
    } finally {
        hideLoading();
    }
}

// 密码一致性实时检查
document.getElementById('confirmUserPassword').addEventListener('input', function() {
    const password = document.getElementById('userPassword').value;
    const confirmPassword = this.value;
    const errorElement = document.getElementById('registerUserError');

    if (password !== confirmPassword) {
        errorElement.textContent = '两次输入的密码不一致';
        errorElement.classList.remove('d-none');
    } else {
        errorElement.classList.add('d-none');
    }
}); 