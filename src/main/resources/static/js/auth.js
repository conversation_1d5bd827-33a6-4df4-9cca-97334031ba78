// 登录表单处理
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const identifier = document.getElementById('studentId').value;
    const password = document.getElementById('password').value;
    const errorElement = document.getElementById('loginError');

    try {
        const response = await fetch('/user/login/password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identifier, password })
        });

        const resp = await response.json();

        console.log(resp)
        console.log(resp.code)
        if (response.ok && resp.code === 200) {
            // 存储登录状态
            localStorage.setItem('user', JSON.stringify(resp.data));
            localStorage.setItem('token', resp.data.token);
            
            // 根据用户角色跳转
            if (resp.data.identity === 'ADMIN') {
                window.location.href = '/admin.html';
            } else {
                window.location.href = '/student.html';
            }
        } else {
            errorElement.textContent = resp.message || '登录失败';
            errorElement.classList.remove('d-none');
        }
    } catch (error) {
        console.error('请求捕获到错误:', error);
        errorElement.textContent = '网络错误，请重试';
        errorElement.classList.remove('d-none');
    }
});

// Toast提示函数
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // 3秒后自动移除
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
} 