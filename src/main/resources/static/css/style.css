/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}

/* 登录注册表单样式 */
.nav-tabs .nav-link {
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 500;
}

/* 课程卡片样式 */
.course-card {
    transition: transform 0.2s;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
}

.course-card .card-body {
    display: flex;
    flex-direction: column;
}

.course-card .card-footer {
    margin-top: auto;
}

/* 课表样式 */
.schedule-grid {
    display: grid;
    grid-template-columns: 100px repeat(5, 1fr);
    gap: 1px;
    background-color: #dee2e6;
}

.schedule-cell {
    background-color: white;
    padding: 10px;
    min-height: 80px;
}

.schedule-cell.header {
    background-color: #f8f9fa;
    font-weight: 500;
    text-align: center;
}

.schedule-cell.selected {
    background-color: #d4edda;
}

.schedule-cell.conflict {
    background-color: #f8d7da;
}

/* 加载动画 */
.loading-spinner {
    width: 3rem;
    height: 3rem;
}

/* Toast提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .schedule-grid {
        grid-template-columns: 80px repeat(5, 1fr);
    }
    
    .schedule-cell {
        padding: 5px;
        font-size: 0.9rem;
    }
} 