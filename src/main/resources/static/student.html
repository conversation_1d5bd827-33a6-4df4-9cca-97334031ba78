<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选课系统 - 学生端</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">选课系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-view="course-list">课程列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-view="schedule">我的课表</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <span class="navbar-text me-3" id="studentName"></span>
                    <button class="btn btn-outline-light" onclick="logout()">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <div class="container mt-4">
        <!-- 课程列表视图 -->
        <div id="courseListView">
            <!-- 搜索和筛选 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索课程...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="teacherFilter">
                        <option value="">选择教师</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="timeFilter">
                        <option value="">选择上课时间</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="sortBy">
                        <option value="name">按课程名称</option>
                        <option value="capacity">按剩余名额</option>
                    </select>
                </div>
            </div>

            <!-- 课程卡片网格 -->
            <div class="row row-cols-1 row-cols-md-3 g-4" id="courseGrid">
                <!-- 课程卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 课表视图 -->
        <div id="scheduleView" class="d-none">
            <div class="schedule-grid">
                <!-- 表头 -->
                <div class="schedule-cell header">时间</div>
                <div class="schedule-cell header">周一</div>
                <div class="schedule-cell header">周二</div>
                <div class="schedule-cell header">周三</div>
                <div class="schedule-cell header">周四</div>
                <div class="schedule-cell header">周五</div>

                <!-- 时间段 -->
                <div class="schedule-cell">8:00-10:00</div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>

                <div class="schedule-cell">10:15-12:15</div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>

                <div class="schedule-cell">14:00-16:00</div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>

                <div class="schedule-cell">16:15-18:15</div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
                <div class="schedule-cell"></div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="position-fixed top-50 start-50 translate-middle d-none" id="loadingSpinner">
        <div class="spinner-border text-primary loading-spinner" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/student.js"></script>
</body>
</html> 