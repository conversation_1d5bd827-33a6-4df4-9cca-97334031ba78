<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选课系统 - 管理员端</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">选课系统 - 管理端</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-view="course-management">课程管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-view="student-management">学生管理</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <span class="navbar-text me-3" id="adminName"></span>
                    <button class="btn btn-outline-light" onclick="logout()">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <div class="container mt-4">
        <!-- 课程管理视图 -->
        <div id="courseManagementView">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>课程管理</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#courseModal">
                    添加课程
                </button>
            </div>

            <!-- 课程列表 -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>课程ID</th>
                            <th>课程名称</th>
                            <th>授课教师</th>
                            <th>上课时间</th>
                            <th>教室</th>
                            <th>容量</th>
                            <th>已选人数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="courseTableBody">
                        <!-- 课程数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 批量操作按钮 -->
            <div class="mt-3">
                <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                <button class="btn btn-success" onclick="exportToExcel()">导出Excel</button>
            </div>
        </div>

        <!-- 学生管理视图 -->
        <div id="studentManagementView" class="d-none">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>学生管理</h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#registerUserModal">
                        注册新用户
                    </button>
                    <input type="text" class="form-control" id="studentSearch" placeholder="搜索学号或姓名">
                    <div class="btn-group">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            状态筛选
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-status="all">全部</a></li>
                            <li><a class="dropdown-item" href="#" data-status="active">正常</a></li>
                            <li><a class="dropdown-item" href="#" data-status="frozen">已冻结</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 学生列表 -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>状态</th>
                            <th>已选课程数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="studentTableBody">
                        <!-- 学生数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 课程表单模态框 -->
    <div class="modal fade" id="courseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加课程</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="courseForm">
                        <input type="hidden" id="courseId">
                        <div class="mb-3">
                            <label for="courseName" class="form-label">课程名称</label>
                            <input type="text" class="form-control" id="courseName" required>
                        </div>
                        <div class="mb-3">
                            <label for="teacher" class="form-label">授课教师</label>
                            <input type="text" class="form-control" id="teacher" required>
                        </div>
                        <div class="mb-3">
                            <label for="time" class="form-label">上课时间</label>
                            <select class="form-select" id="time" required>
                                <option value="">选择时间</option>
                                <!-- 周一 -->
                                <option value="周一 8:00-10:00">周一 8:00-10:00</option>
                                <option value="周一 10:15-12:15">周一 10:15-12:15</option>
                                <option value="周一 14:00-16:00">周一 14:00-16:00</option>
                                <option value="周一 16:15-18:15">周一 16:15-18:15</option>
                                <!-- 周二 -->
                                <option value="周二 8:00-10:00">周二 8:00-10:00</option>
                                <option value="周二 10:15-12:15">周二 10:15-12:15</option>
                                <option value="周二 14:00-16:00">周二 14:00-16:00</option>
                                <option value="周二 16:15-18:15">周二 16:15-18:15</option>
                                <!-- 周三 -->
                                <option value="周三 8:00-10:00">周三 8:00-10:00</option>
                                <option value="周三 10:15-12:15">周三 10:15-12:15</option>
                                <option value="周三 14:00-16:00">周三 14:00-16:00</option>
                                <option value="周三 16:15-18:15">周三 16:15-18:15</option>
                                <!-- 周四 -->
                                <option value="周四 8:00-10:00">周四 8:00-10:00</option>
                                <option value="周四 10:15-12:15">周四 10:15-12:15</option>
                                <option value="周四 14:00-16:00">周四 14:00-16:00</option>
                                <option value="周四 16:15-18:15">周四 16:15-18:15</option>
                                <!-- 周五 -->
                                <option value="周五 8:00-10:00">周五 8:00-10:00</option>
                                <option value="周五 10:15-12:15">周五 10:15-12:15</option>
                                <option value="周五 14:00-16:00">周五 14:00-16:00</option>
                                <option value="周五 16:15-18:15">周五 16:15-18:15</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="classroom" class="form-label">教室</label>
                            <input type="text" class="form-control" id="classroom" required>
                        </div>
                        <div class="mb-3">
                            <label for="capacity" class="form-label">容量</label>
                            <input type="number" class="form-control" id="capacity" min="1" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCourse()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册用户模态框 -->
    <div class="modal fade" id="registerUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">注册新用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerUserForm">
                        <div class="mb-3">
                            <label for="userId" class="form-label">学号/工号</label>
                            <input type="text" class="form-control" id="userId" required>
                        </div>
                        <div class="mb-3">
                            <label for="userName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmUserPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="confirmUserPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="userRole" class="form-label">用户角色</label>
                            <select class="form-select" id="userRole" required>
                                <option value="STUDENT">学生</option>
                                <option value="ADMIN">管理员</option>
                            </select>
                        </div>
                        <div class="alert alert-danger d-none" id="registerUserError"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="registerUser()">注册</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="position-fixed top-50 start-50 translate-middle d-none" id="loadingSpinner">
        <div class="spinner-border text-primary loading-spinner" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/admin.js"></script>
</body>
</html> 