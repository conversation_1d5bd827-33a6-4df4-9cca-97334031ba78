-- 设置客户端与服务器之间的字符集为utf8mb4，这个字符集可以存储任何Unicode字符。
SET NAMES utf8mb4;
-- 关闭外键约束检查，这通常在创建或修改表结构时使用，以避免由于外键约束而导致的创建失败。
SET
    FOREIGN_KEY_CHECKS = 0;

drop
    database IF EXISTS `course_system`;
create DATABASE `course_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE
    `course_system`;

drop table IF EXISTS `user`;
create TABLE `user`
(
    `id`           bigint UNSIGNED                                               NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `user_id`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '学工号',
    `username`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '用户名',
    `email`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '邮箱',
    `phone_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '手机号',
    `password`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL     DEFAULT NULL comment '登录密码',
    `identity`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '用户身份',
    `status`       varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'active' comment '用户状态',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_user_id` (`user_id`) USING BTREE,
    UNIQUE INDEX `uk_username` (`username`(30) ASC) USING BTREE,
    UNIQUE INDEX `uk_email` (`email`(30) ASC) USING BTREE,
    UNIQUE INDEX `uk_phone_number` (`phone_number`(11) ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;

drop table if exists `course`;
create table `course`
(
    `id`             bigint UNSIGNED                                               NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`     datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `course_name`    varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '课程名称',
    `teacher`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '授课教师',
    `time`           datetime                                                      NOT NULL comment '上课时间',
    `classroom`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '教室',
    `capacity`       int                                                           NOT NULL comment '容量',
    `selected_count` int                                                           NOT NULL DEFAULT 0 comment '已选人数',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_course_name` (`course_name`(30) ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;

drop table if exists `course_user`;
create table `course_user`
(
    `id`           bigint UNSIGNED NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`   datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified` datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `course_id`    bigint UNSIGNED NOT NULL comment '课程ID',
    `user_id`      bigint UNSIGNED NOT NULL comment '用户ID',
    `course_name`  varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '课程名称',
    `teacher`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '授课教师',
    `time`         datetime        NOT NULL comment '上课时间',
    `classroom`    varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '教室',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_course_user` (`course_id` ASC, `user_id` ASC) USING BTREE,
    INDEX `idx_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;


-- SET FOREIGN_KEY_CHECKS = 1;：在脚本的最后，重新开启外键约束检查。
SET
    FOREIGN_KEY_CHECKS = 1;