-- 设置客户端与服务器之间的字符集为utf8mb4，这个字符集可以存储任何Unicode字符。
SET NAMES utf8mb4;
-- 关闭外键约束检查，这通常在创建或修改表结构时使用，以避免由于外键约束而导致的创建失败。
SET
    FOREIGN_KEY_CHECKS = 0;

drop
    database IF EXISTS `course_system`;
create DATABASE `course_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE
    `course_system`;

drop table IF EXISTS `user`;
create TABLE `user`
(
    `id`           bigint UNSIGNED                                               NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `user_id`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '学工号',
    `username`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '用户名',
    `email`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '邮箱',
    `phone_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '手机号',
    `password`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL     DEFAULT NULL comment '登录密码',
    `identity`     varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '用户身份',
    `status`       varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'active' comment '用户状态',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_user_id` (`user_id`) USING BTREE,
    UNIQUE INDEX `uk_username` (`username`(30) ASC) USING BTREE,
    UNIQUE INDEX `uk_email` (`email`(30) ASC) USING BTREE,
    UNIQUE INDEX `uk_phone_number` (`phone_number`(11) ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;

drop table if exists `course`;
create table `course`
(
    `id`             bigint UNSIGNED                                               NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`     datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `course_name`    varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '课程名称',
    `teacher`        varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '授课教师',
    `time`           datetime                                                      NOT NULL comment '上课时间',
    `classroom`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '教室',
    `capacity`       int                                                           NOT NULL comment '容量',
    `selected_count` int                                                           NOT NULL DEFAULT 0 comment '已选人数',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_course_name` (`course_name`(30) ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;

drop table if exists `course_user`;
create table `course_user`
(
    `id`           bigint UNSIGNED NOT NULL AUTO_INCREMENT comment '主键',
    `gmt_create`   datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    `gmt_modified` datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON update CURRENT_TIMESTAMP comment '更新时间',
    `course_id`    bigint UNSIGNED NOT NULL comment '课程ID',
    `user_id`      bigint UNSIGNED NOT NULL comment '用户ID',
    `course_name`  varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '课程名称',
    `teacher`      varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '授课教师',
    `time`         datetime        NOT NULL comment '上课时间',
    `classroom`    varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL comment '教室',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_course_user` (`course_id` ASC, `user_id` ASC) USING BTREE,
    INDEX `idx_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb3
  COLLATE = utf8mb3_general_ci
  ROW_FORMAT = DYNAMIC;


-- 插入测试用户数据
INSERT INTO `user` (`user_id`, `username`, `email`, `phone_number`, `password`, `identity`, `status`) VALUES
-- 管理员账户 (用户名: admin, 密码: 123456)
('admin001', '系统管理员', '<EMAIL>', '13800000001', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'ADMIN', 'active'),
-- 学生账户 (密码都是: 123456)
('2021001', '张三', '<EMAIL>', '13800000002', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021002', '李四', '<EMAIL>', '13800000003', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021003', '王五', '<EMAIL>', '13800000004', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021004', '赵六', '<EMAIL>', '13800000005', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021005', '钱七', '<EMAIL>', '13800000006', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
-- 更多学生账户
('2021006', '孙八', '<EMAIL>', '13800000007', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021007', '周九', '<EMAIL>', '13800000008', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021008', '吴十', '<EMAIL>', '13800000009', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021009', '郑十一', '<EMAIL>', '13800000010', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
('2021010', '王十二', '<EMAIL>', '13800000011', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'STUDENT', 'active'),
-- 更多管理员账户
('admin002', '教务管理员', '<EMAIL>', '13800000012', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'ADMIN', 'active'),
('admin003', '系统管理员2', '<EMAIL>', '13800000013', 'a1b2c3d4e5f6789012345678901234567890abcdeftestsalt123456789012345678901234', 'ADMIN', 'active');

-- 插入测试课程数据
INSERT INTO `course` (`course_name`, `teacher`, `time`, `classroom`, `capacity`, `selected_count`) VALUES
('高等数学A', '张教授', '2024-01-15 08:00:00', 'A101', 50, 15),
('线性代数', '李教授', '2024-01-15 10:00:00', 'A102', 40, 20),
('概率论与数理统计', '王教授', '2024-01-15 14:00:00', 'A103', 45, 10),
('大学物理', '刘教授', '2024-01-16 08:00:00', 'B201', 60, 25),
('程序设计基础', '陈教授', '2024-01-16 10:00:00', 'C301', 35, 30),
('数据结构', '杨教授', '2024-01-16 14:00:00', 'C302', 40, 18),
('计算机网络', '周教授', '2024-01-17 08:00:00', 'C303', 45, 22),
('操作系统', '吴教授', '2024-01-17 10:00:00', 'C304', 40, 16),
('数据库原理', '郑教授', '2024-01-17 14:00:00', 'C305', 50, 28),
('软件工程', '孙教授', '2024-01-18 08:00:00', 'C306', 35, 12),
-- 更多课程数据
('算法设计与分析', '马教授', '2024-01-18 10:00:00', 'C307', 40, 8),
('人工智能导论', '冯教授', '2024-01-18 14:00:00', 'C308', 30, 25),
('机器学习', '许教授', '2024-01-19 08:00:00', 'C309', 35, 20),
('深度学习', '何教授', '2024-01-19 10:00:00', 'C310', 25, 15),
('Web开发技术', '谢教授', '2024-01-19 14:00:00', 'C311', 45, 30),
('移动应用开发', '韩教授', '2024-01-20 08:00:00', 'C312', 40, 18),
('云计算技术', '邓教授', '2024-01-20 10:00:00', 'C313', 35, 12),
('大数据分析', '彭教授', '2024-01-20 14:00:00', 'C314', 50, 35),
('网络安全', '曾教授', '2024-01-21 08:00:00', 'C315', 30, 22),
('区块链技术', '萧教授', '2024-01-21 10:00:00', 'C316', 25, 10);

-- 插入一些选课记录（学生已选的课程）
INSERT INTO `course_user` (`course_id`, `user_id`, `course_name`, `teacher`, `time`, `classroom`) VALUES
-- 张三(id=2)选的课程
(1, 2, '高等数学A', '张教授', '2024-01-15 08:00:00', 'A101'),
(2, 2, '线性代数', '李教授', '2024-01-15 10:00:00', 'A102'),
(5, 2, '程序设计基础', '陈教授', '2024-01-16 10:00:00', 'C301'),
-- 李四(id=3)选的课程
(1, 3, '高等数学A', '张教授', '2024-01-15 08:00:00', 'A101'),
(3, 3, '概率论与数理统计', '王教授', '2024-01-15 14:00:00', 'A103'),
(4, 3, '大学物理', '刘教授', '2024-01-16 08:00:00', 'B201'),
-- 王五(id=4)选的课程
(2, 4, '线性代数', '李教授', '2024-01-15 10:00:00', 'A102'),
(6, 4, '数据结构', '杨教授', '2024-01-16 14:00:00', 'C302'),
-- 赵六(id=5)选的课程
(4, 5, '大学物理', '刘教授', '2024-01-16 08:00:00', 'B201'),
(7, 5, '计算机网络', '周教授', '2024-01-17 08:00:00', 'C303'),
(9, 5, '数据库原理', '郑教授', '2024-01-17 14:00:00', 'C305'),
-- 钱七(id=6)选的课程
(3, 6, '概率论与数理统计', '王教授', '2024-01-15 14:00:00', 'A103'),
(8, 6, '操作系统', '吴教授', '2024-01-17 10:00:00', 'C304'),
(11, 6, '算法设计与分析', '马教授', '2024-01-18 10:00:00', 'C307'),
-- 孙八(id=7)选的课程
(5, 7, '程序设计基础', '陈教授', '2024-01-16 10:00:00', 'C301'),
(6, 7, '数据结构', '杨教授', '2024-01-16 14:00:00', 'C302'),
(12, 7, '人工智能导论', '冯教授', '2024-01-18 14:00:00', 'C308'),
-- 周九(id=8)选的课程
(1, 8, '高等数学A', '张教授', '2024-01-15 08:00:00', 'A101'),
(4, 8, '大学物理', '刘教授', '2024-01-16 08:00:00', 'B201'),
(13, 8, '机器学习', '许教授', '2024-01-19 08:00:00', 'C309'),
-- 吴十(id=9)选的课程
(2, 9, '线性代数', '李教授', '2024-01-15 10:00:00', 'A102'),
(7, 9, '计算机网络', '周教授', '2024-01-17 08:00:00', 'C303'),
(15, 9, 'Web开发技术', '谢教授', '2024-01-19 14:00:00', 'C311'),
-- 郑十一(id=10)选的课程
(10, 10, '软件工程', '孙教授', '2024-01-18 08:00:00', 'C306'),
(14, 10, '深度学习', '何教授', '2024-01-19 10:00:00', 'C310'),
(18, 10, '大数据分析', '彭教授', '2024-01-20 14:00:00', 'C314');

-- SET FOREIGN_KEY_CHECKS = 1;：在脚本的最后，重新开启外键约束检查。
SET
    FOREIGN_KEY_CHECKS = 1;